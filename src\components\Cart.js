import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';

function Cart() {
  const {
    cart,
    loading,
    totalPrice,
    totalItems,
    updateCartItem,
    removeFromCart,
    clearCart,
    fetchCart
  } = useCart();

  useEffect(() => {
    fetchCart();
  }, [fetchCart]);

  const handleQuantityChange = async (posterId, newQuantity) => {
    if (newQuantity < 1) {
      await removeFromCart(posterId);
    } else {
      await updateCartItem(posterId, newQuantity);
    }
  };

  const handleRemoveItem = async (posterId) => {
    await removeFromCart(posterId);
  };

  const handleClearCart = async () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      await clearCart();
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(price);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-primary-dark to-secondary-dark flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-red"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-primary-dark to-secondary-dark py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-pure-white mb-4">
            🛒 Shopping Cart
          </h1>
          <p className="text-neutral-medium">
            {totalItems > 0 ? `${totalItems} item${totalItems > 1 ? 's' : ''} in your cart` : 'Your cart is empty'}
          </p>
        </div>

        {!cart?.items || cart.items.length === 0 ? (
          <div className="text-center py-16">
            <div className="mb-8">
              <svg className="mx-auto h-24 w-24 text-neutral-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-pure-white mb-4">Your cart is empty</h3>
            <p className="text-neutral-medium mb-8">
              Discover our collection of underrated posters and add some to your cart!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/featured-posters"
                className="bg-accent-red hover:bg-accent-red-dark text-pure-white px-6 py-3 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                Browse Music Posters
              </Link>
              <Link
                to="/movies-posters"
                className="border border-red-600 text-red-400 hover:bg-red-600 hover:text-white px-6 py-3 rounded-lg transition-colors duration-200"
              >
                Browse Movie Posters
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Cart Items */}
            <div className="bg-gray-800 rounded-lg overflow-hidden">
              {cart.items.map((item) => (
                <div key={item.id} className="flex items-center p-6 border-b border-gray-700 last:border-b-0">
                  <img
                    src={item.poster?.image_path || '/placeholder-poster.jpg'}
                    alt={item.poster?.title}
                    className="w-20 h-20 object-cover rounded-lg"
                    onError={(e) => {
                      e.target.src = '/placeholder-poster.jpg';
                    }}
                  />

                  <div className="flex-1 ml-6">
                    <h3 className="text-white font-semibold text-lg">
                      {item.poster?.title}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      {item.poster?.description}
                    </p>
                    <p className="text-gray-400 text-sm">
                      Size: {item.poster?.size} | Frame: {item.poster?.frame_type}
                    </p>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleQuantityChange(item.poster_id, item.quantity - 1)}
                        className="bg-gray-700 hover:bg-gray-600 text-white w-8 h-8 rounded-full flex items-center justify-center"
                      >
                        -
                      </button>
                      <span className="text-white font-semibold w-8 text-center">
                        {item.quantity}
                      </span>
                      <button
                        onClick={() => handleQuantityChange(item.poster_id, item.quantity + 1)}
                        className="bg-gray-700 hover:bg-gray-600 text-white w-8 h-8 rounded-full flex items-center justify-center"
                      >
                        +
                      </button>
                    </div>

                    <div className="text-right">
                      <p className="text-white font-semibold">
                        {formatPrice(item.poster?.price * item.quantity)}
                      </p>
                      <p className="text-gray-400 text-sm">
                        {formatPrice(item.poster?.price)} each
                      </p>
                    </div>

                    <button
                      onClick={() => handleRemoveItem(item.poster_id)}
                      className="text-red-400 hover:text-red-300 p-2"
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Cart Summary */}
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex justify-between items-center mb-4">
                <span className="text-lg font-semibold text-white">Total</span>
                <span className="text-2xl font-bold text-white">
                  {formatPrice(totalPrice)}
                </span>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={handleClearCart}
                  className="flex-1 border border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white px-6 py-3 rounded-lg transition-colors duration-200"
                >
                  Clear Cart
                </button>
                <Link
                  to="/checkout"
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg text-center transition-colors duration-200"
                >
                  Proceed to Checkout
                </Link>
              </div>

              <p className="text-gray-400 text-sm mt-4 text-center">
                💰 Payment: Cash on Delivery | 🚚 Free shipping across Morocco
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Cart;
