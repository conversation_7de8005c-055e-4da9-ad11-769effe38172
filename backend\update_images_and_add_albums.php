<?php

/**
 * Update Image Paths and Add Album Posters
 * Fixes image paths and adds new album poster category
 */

echo "🎨 Updating I-POSTER Images and Adding Album Posters\n";
echo "===================================================\n\n";

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database successfully!\n\n";
    
    // Step 1: Update existing music posters with correct image paths
    echo "🔧 Step 1: Updating existing music poster image paths...\n";
    
    $musicUpdates = [
        1 => '/images/posters/poster1.jpg',      // Blonde
        2 => '/images/posters/poster2.jpg',      // Madvillainy  
        3 => '/images/posters/godfather.jpg',    // In the Aeroplane Over the Sea
        4 => '/images/posters/poster2.png',      // Cosmogramma
        5 => '/images/posters/peaky blinders.jpg', // Spiderland
        6 => '/images/posters/men in black.jpg'  // Since I Left You
    ];
    
    foreach ($musicUpdates as $id => $imagePath) {
        $stmt = $pdo->prepare("UPDATE posters SET image_path = ? WHERE id = ?");
        $stmt->execute([$imagePath, $id]);
    }
    echo "✅ Updated " . count($musicUpdates) . " music poster image paths!\n\n";
    
    // Step 2: Add album category to enum if not exists
    echo "🔧 Step 2: Adding album category to database...\n";
    try {
        $pdo->exec("ALTER TABLE posters MODIFY COLUMN category enum('music','movie','art','sports','vintage','album') NOT NULL");
        echo "✅ Added 'album' category to database!\n";
    } catch (Exception $e) {
        echo "ℹ️  Album category may already exist: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // Step 3: Add album posters
    echo "🎵 Step 3: Adding album posters...\n";
    
    $albumPosters = [
        [
            'title' => 'The Dark Side of the Moon',
            'description' => 'Pink Floyd\'s legendary concept album exploring themes of conflict, greed, and mental illness.',
            'price' => 135.00,
            'image_path' => '/images/posters/poster1.jpg',
            'category' => 'album',
            'artist' => 'Pink Floyd',
            'featured' => 1,
            'tags' => json_encode(['progressive rock', 'concept album', 'classic', 'psychedelic'])
        ],
        [
            'title' => 'Abbey Road',
            'description' => 'The Beatles\' iconic final studio album featuring the famous crosswalk cover.',
            'price' => 140.00,
            'image_path' => '/images/posters/poster2.jpg',
            'category' => 'album',
            'artist' => 'The Beatles',
            'featured' => 1,
            'tags' => json_encode(['rock', 'pop', 'classic', 'iconic'])
        ],
        [
            'title' => 'Nevermind',
            'description' => 'Nirvana\'s breakthrough album that brought grunge to the mainstream.',
            'price' => 125.00,
            'image_path' => '/images/posters/poster2.png',
            'category' => 'album',
            'artist' => 'Nirvana',
            'featured' => 0,
            'tags' => json_encode(['grunge', 'alternative rock', '90s', 'iconic'])
        ],
        [
            'title' => 'Thriller',
            'description' => 'Michael Jackson\'s best-selling album of all time with unforgettable hits.',
            'price' => 130.00,
            'image_path' => '/images/posters/godfather.jpg',
            'category' => 'album',
            'artist' => 'Michael Jackson',
            'featured' => 1,
            'tags' => json_encode(['pop', 'r&b', 'dance', 'classic'])
        ],
        [
            'title' => 'OK Computer',
            'description' => 'Radiohead\'s masterpiece exploring technology and modern alienation.',
            'price' => 120.00,
            'image_path' => '/images/posters/peaky blinders.jpg',
            'category' => 'album',
            'artist' => 'Radiohead',
            'featured' => 0,
            'tags' => json_encode(['alternative rock', 'experimental', 'art rock', 'influential'])
        ],
        [
            'title' => 'The Velvet Underground & Nico',
            'description' => 'The influential debut album with Andy Warhol\'s iconic banana cover.',
            'price' => 115.00,
            'image_path' => '/images/posters/men in black.jpg',
            'category' => 'album',
            'artist' => 'The Velvet Underground',
            'featured' => 1,
            'tags' => json_encode(['art rock', 'experimental', 'influential', 'underground'])
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO posters (
            title, description, price, image_path, category, artist, featured, 
            size, frame_type, tags, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'A3', 'black', ?, 'active', NOW(), NOW())
    ");
    
    foreach ($albumPosters as $poster) {
        $stmt->execute([
            $poster['title'],
            $poster['description'],
            $poster['price'],
            $poster['image_path'],
            $poster['category'],
            $poster['artist'],
            $poster['featured'],
            $poster['tags']
        ]);
    }
    echo "✅ Added " . count($albumPosters) . " album posters!\n\n";
    
    // Step 4: Update some existing music posters to be featured
    echo "⭐ Step 4: Updating featured status for music posters...\n";
    $featuredMusicIds = [1, 2, 6]; // Blonde, Madvillainy, Since I Left You
    foreach ($featuredMusicIds as $id) {
        $stmt = $pdo->prepare("UPDATE posters SET featured = 1 WHERE id = ?");
        $stmt->execute([$id]);
    }
    echo "✅ Updated featured status for music posters!\n\n";
    
    // Step 5: Verify the updates
    echo "🔍 Step 5: Verifying updates...\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posters WHERE category = 'music'");
    $musicCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ Music posters: $musicCount\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posters WHERE category = 'album'");
    $albumCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ Album posters: $albumCount\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posters WHERE featured = 1");
    $featuredCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ Featured posters: $featuredCount\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posters WHERE category = 'movie'");
    $movieCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ Movie posters: $movieCount\n\n";
    
    echo "🎉 Database update completed successfully!\n";
    echo "==========================================\n\n";
    
    echo "📋 Summary:\n";
    echo "• Fixed image paths for existing music posters\n";
    echo "• Added 'album' category to database\n";
    echo "• Added 6 new album posters\n";
    echo "• Updated featured status for selected posters\n";
    echo "• All images now point to /images/posters/ directory\n\n";
    
    echo "🎨 Categories available:\n";
    echo "• Music Posters: $musicCount items\n";
    echo "• Album Posters: $albumCount items\n";
    echo "• Movie Posters: $movieCount items\n";
    echo "• Featured Posters: $featuredCount items\n\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

?>
