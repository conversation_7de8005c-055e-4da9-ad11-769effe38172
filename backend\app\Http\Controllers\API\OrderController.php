<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        $query = Order::with(['items.poster']);
        
        // If user is authenticated, show only their orders
        if ($request->user() && !$request->user()->isAdmin()) {
            $query->where('user_id', $request->user()->id);
        }
        
        // Filter by status
        if ($request->has('status')) {
            $query->byStatus($request->status);
        }
        
        // Search by order number or customer info
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }
        
        return $query->orderBy('created_at', 'desc')->paginate(15);
    }

    public function show(Request $request, $id)
    {
        $query = Order::with(['items.poster']);
        
        // If user is authenticated and not admin, ensure they can only see their orders
        if ($request->user() && !$request->user()->isAdmin()) {
            $query->where('user_id', $request->user()->id);
        }
        
        $order = $query->findOrFail($id);
        
        return response()->json($order);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'nullable|email|max:255',
            'customer_phone' => 'required|string|max:20',
            'delivery_address' => 'required|string',
            'notes' => 'nullable|string|max:1000'
        ]);

        // Get cart
        $cart = $this->getCart($request);
        
        if (!$cart || $cart->items->isEmpty()) {
            return response()->json([
                'message' => 'Cart is empty'
            ], 400);
        }

        // Check stock availability
        foreach ($cart->items as $item) {
            if ($item->poster->quantity < $item->quantity) {
                return response()->json([
                    'message' => "Not enough stock for {$item->poster->title}"
                ], 400);
            }
        }

        DB::beginTransaction();
        
        try {
            // Create order
            $order = Order::create([
                'user_id' => $request->user()?->id,
                'order_number' => '', // Will be generated after creation
                'customer_name' => $validated['customer_name'],
                'customer_email' => $validated['customer_email'],
                'customer_phone' => $validated['customer_phone'],
                'delivery_address' => $validated['delivery_address'],
                'notes' => $validated['notes'],
                'total_amount' => $cart->getTotalPrice(),
                'payment_type' => 'Cash on Delivery',
                'status' => 'pending'
            ]);

            // Generate order number
            $order->generateOrderNumber();

            // Create order items and update stock
            foreach ($cart->items as $item) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'poster_id' => $item->poster->id,
                    'quantity' => $item->quantity,
                    'price' => $item->poster->price,
                    'poster_title' => $item->poster->title,
                    'poster_description' => $item->poster->description
                ]);

                // Update poster stock
                $item->poster->decrement('quantity', $item->quantity);
            }

            // Clear cart
            $cart->clear();

            DB::commit();

            return response()->json([
                'message' => 'Order placed successfully',
                'order' => $order->load(['items.poster'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'message' => 'Failed to place order',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateStatus(Request $request, $id)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,confirmed,shipped,delivered,cancelled',
            'admin_notes' => 'nullable|string'
        ]);

        $order = Order::findOrFail($id);
        
        $order->update([
            'status' => $validated['status'],
            'admin_notes' => $validated['admin_notes'] ?? $order->admin_notes
        ]);

        return response()->json([
            'message' => 'Order status updated',
            'order' => $order
        ]);
    }

    private function getCart(Request $request)
    {
        if ($request->user()) {
            return Cart::where('user_id', $request->user()->id)->with(['items.poster'])->first();
        } else {
            $sessionId = $request->header('X-Session-ID') ?? session()->getId();
            return Cart::where('session_id', $sessionId)->with(['items.poster'])->first();
        }
    }
}
