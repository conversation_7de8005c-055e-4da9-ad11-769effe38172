import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Add session ID for guest users
  const sessionId = localStorage.getItem('session_id') || generateSessionId();
  config.headers['X-Session-ID'] = sessionId;

  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Generate session ID for guest users
function generateSessionId() {
  const sessionId = 'guest_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  localStorage.setItem('session_id', sessionId);
  return sessionId;
}

// Auth API
export const authAPI = {
  register: (userData) => api.post('/register', userData),
  login: (credentials) => api.post('/login', credentials),
  logout: () => api.post('/logout'),
  getUser: () => api.get('/user'),
  updateProfile: (userData) => api.put('/user/profile', userData),
  changePassword: (passwordData) => api.put('/user/password', passwordData),
};

// Posters API
export const postersAPI = {
  getAll: (params = {}) => api.get('/posters', { params }),
  getById: (id) => api.get(`/posters/${id}`),
  create: (posterData) => api.post('/posters', posterData),
  update: (id, posterData) => api.put(`/posters/${id}`, posterData),
  delete: (id) => api.delete(`/posters/${id}`),
};

// Cart API
export const cartAPI = {
  get: () => api.get('/cart'),
  addItem: (posterId, quantity = 1) => api.post('/cart/add', { poster_id: posterId, quantity }),
  updateItem: (posterId, quantity) => api.put(`/cart/items/${posterId}`, { quantity }),
  removeItem: (posterId) => api.delete(`/cart/items/${posterId}`),
  clear: () => api.delete('/cart/clear'),
};

// Orders API
export const ordersAPI = {
  getAll: (params = {}) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  create: (orderData) => api.post('/orders', orderData),
  updateStatus: (id, status, adminNotes) => api.put(`/orders/${id}/status`, { status, admin_notes: adminNotes }),
};

// Admin API
export const adminAPI = {
  getDashboard: () => api.get('/admin/dashboard'),
  getUsers: (params = {}) => api.get('/admin/users', { params }),
  updateUserRole: (userId, role) => api.put(`/admin/users/${userId}/role`, { role }),
  uploadImage: (imageFile) => {
    const formData = new FormData();
    formData.append('image', imageFile);
    return api.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

export default api;
