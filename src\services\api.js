import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Add session ID for guest users
  const sessionId = localStorage.getItem('cart_session_id') || generateSessionId();
  config.headers['X-Session-ID'] = sessionId;

  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Generate session ID for guest users
function generateSessionId() {
  const sessionId = 'guest_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  localStorage.setItem('cart_session_id', sessionId);
  return sessionId;
}

// Auth API
export const authAPI = {
  register: (userData) => api.post('/api/auth/register', userData),
  login: (credentials) => api.post('/api/auth/login', credentials),
  logout: () => api.post('/api/auth/logout'),
  getUser: () => api.get('/api/user'),
  updateProfile: (userData) => api.put('/api/user/profile', userData),
  changePassword: (passwordData) => api.put('/api/user/password', passwordData),
};

// Posters API
export const postersAPI = {
  getAll: (params = {}) => api.get('/api/posters', { params }),
  getById: (id) => api.get(`/api/posters/${id}`),
  create: (posterData) => api.post('/api/posters', posterData),
  update: (id, posterData) => api.put(`/api/posters/${id}`, posterData),
  delete: (id) => api.delete(`/api/posters/${id}`),
};

// Cart API
export const cartAPI = {
  get: () => api.get('/api/cart'),
  addItem: (posterId, quantity = 1) => api.post('/api/cart/add', { poster_id: posterId, quantity }),
  updateItem: (posterId, quantity) => api.put(`/api/cart/items/${posterId}`, { quantity }),
  removeItem: (posterId) => api.delete(`/api/cart/items/${posterId}`),
  clear: () => api.delete('/api/cart'),
};

// Orders API
export const ordersAPI = {
  getAll: (params = {}) => api.get('/api/orders', { params }),
  getById: (id) => api.get(`/api/orders/${id}`),
  create: (orderData) => api.post('/api/orders', orderData),
  updateStatus: (id, status, adminNotes) => api.put(`/api/orders/${id}/status`, { status, admin_notes: adminNotes }),
};

// Admin API
export const adminAPI = {
  getDashboard: () => api.get('/api/admin/dashboard'),
  getUsers: (params = {}) => api.get('/api/admin/users', { params }),
  updateUserRole: (userId, role) => api.put(`/api/admin/users/${userId}/role`, { role }),
  uploadImage: (imageFile) => {
    const formData = new FormData();
    formData.append('image', imageFile);
    return api.post('/api/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

export default api;
