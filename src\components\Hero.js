import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { postersAPI } from "../services/api";
import PosterCard from "./PosterCard";
import Slider from "react-slick";
// CSS files are loaded from public/slick.css to avoid PostCSS processing

function Hero() {
  const [featuredPosters, setFeaturedPosters] = useState([]);
  const [musicPosters, setMusicPosters] = useState([]);
  const [moviePosters, setMoviePosters] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPosters();
  }, []);

  const fetchPosters = async () => {
    try {
      setLoading(true);

      // Fetch all posters for featured section
      const allResponse = await postersAPI.getAll();
      if (allResponse.data.success && allResponse.data.data) {
        setFeaturedPosters(allResponse.data.data.slice(0, 6));
      }

      // Fetch music posters
      const musicResponse = await postersAPI.getAll({ category: 'music' });
      if (musicResponse.data.success && musicResponse.data.data) {
        setMusicPosters(musicResponse.data.data.slice(0, 6));
      }

      // Fetch movie posters
      const movieResponse = await postersAPI.getAll({ category: 'movie' });
      if (movieResponse.data.success && movieResponse.data.data) {
        setMoviePosters(movieResponse.data.data.slice(0, 6));
      }

    } catch (error) {
      console.error('Error fetching posters:', error);
    } finally {
      setLoading(false);
    }
  };

  const sliderSettings = {
    infinite: true,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    dots: true,
    arrows: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh' }}>
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content-left">
          <h1 className="hero-quote">
            "Bring your walls to life with art that speaks to you. Discover the perfect poster to express your style."
          </h1>
        </div>
      </section>

      {/* Featured Posters Section */}
      {featuredPosters.length > 0 && (
        <section className="posters-section">
          <div className="container">
            <div className="section-header">
              <h2 className="section-title">Featured Posters</h2>
              <p className="section-subtitle">
                Handpicked selections from our premium collection
              </p>
            </div>
            <div className="poster-slider">
              <Slider {...sliderSettings}>
                {featuredPosters.map((poster) => (
                  <div key={poster.id} style={{ padding: '0 10px' }}>
                    <PosterCard poster={poster} />
                  </div>
                ))}
              </Slider>
            </div>
          </div>
        </section>
      )}

      {/* Music Posters Section */}
      {musicPosters.length > 0 && (
        <section className="py-16 bg-black">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center mb-12">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Music Posters
                </h2>
                <p className="text-gray-400 text-lg">
                  Express your musical taste with iconic album artwork
                </p>
              </div>
              <Link
                to="/music-posters"
                className="text-red-400 hover:text-red-300 font-semibold flex items-center"
              >
                View All
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
            <div className="poster-slider">
              <Slider {...sliderSettings}>
                {musicPosters.map((poster) => (
                  <div key={poster.id} className="px-2">
                    <PosterCard poster={poster} />
                  </div>
                ))}
              </Slider>
            </div>
          </div>
        </section>
      )}

      {/* Movie Posters Section */}
      {moviePosters.length > 0 && (
        <section className="py-16 bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center mb-12">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Movies & Series Posters
                </h2>
                <p className="text-gray-400 text-lg">
                  Celebrate your favorite films and shows
                </p>
              </div>
              <Link
                to="/movies-posters"
                className="text-red-400 hover:text-red-300 font-semibold flex items-center"
              >
                View All
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
            <div className="poster-slider">
              <Slider {...sliderSettings}>
                {moviePosters.map((poster) => (
                  <div key={poster.id} className="px-2">
                    <PosterCard poster={poster} />
                  </div>
                ))}
              </Slider>
            </div>
          </div>
        </section>
      )}

      {/* Call to Action Section */}
      <section className="py-16 bg-red-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Space?
          </h2>
          <p className="text-red-100 text-lg mb-8">
            Browse our complete collection and find the perfect poster for your style
          </p>
          <Link
            to="/music-posters"
            className="bg-white text-red-600 hover:bg-gray-100 px-8 py-3 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105"
          >
            Start Shopping
          </Link>
        </div>
      </section>
    </div>
  );
}

export default Hero;
