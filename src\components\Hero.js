import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { postersAPI } from "../services/api";
import PosterCard from "./PosterCard";
// import Slider from "react-slick";
// import 'slick-carousel/slick/slick.css';
// import 'slick-carousel/slick/slick-theme.css';

function Hero() {
  const [featuredPosters, setFeaturedPosters] = useState([]);
  const [musicPosters, setMusicPosters] = useState([]);
  const [moviePosters, setMoviePosters] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPosters();
  }, []);

  const fetchPosters = async () => {
    try {
      setLoading(true);

      // Fetch featured posters
      const featuredResponse = await postersAPI.getAll({ featured: true, per_page: 6 });
      setFeaturedPosters(featuredResponse.data.data || []);

      // Fetch music posters
      const musicResponse = await postersAPI.getAll({ category: 'music', per_page: 6 });
      setMusicPosters(musicResponse.data.data || []);

      // Fetch movie posters
      const movieResponse = await postersAPI.getAll({ category: 'movie', per_page: 6 });
      setMoviePosters(movieResponse.data.data || []);

    } catch (error) {
      console.error('Error fetching posters:', error);
    } finally {
      setLoading(false);
    }
  };

  // Temporarily disabled slider for PostCSS compatibility
  // const sliderSettings = {
  //   infinite: true,
  //   slidesToShow: 3,
  //   slidesToScroll: 1,
  //   autoplay: true,
  //   autoplaySpeed: 3000,
  //   dots: true,
  //   arrows: true,
  //   responsive: [
  //     {
  //       breakpoint: 1024,
  //       settings: {
  //         slidesToShow: 2,
  //       },
  //     },
  //     {
  //       breakpoint: 600,
  //       settings: {
  //         slidesToShow: 1,
  //       },
  //     },
  //   ],
  // };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-r from-black via-gray-900 to-black">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
          style={{
            backgroundImage: `url(${require('../posters image/bg poster.jpg')})`
          }}
        ></div>
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-in">
            I-POSTER
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 animate-slide-up">
            "Bring your walls to life with art that speaks to you. Discover the perfect poster to express your style."
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/music-posters"
              className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              Shop Music Posters
            </Link>
            <Link
              to="/movies-posters"
              className="border-2 border-white text-white hover:bg-white hover:text-black px-8 py-3 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              Shop Movie Posters
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Posters Section */}
      {featuredPosters.length > 0 && (
        <section className="py-16 bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Featured Posters
              </h2>
              <p className="text-gray-400 text-lg">
                Handpicked selections from our premium collection
              </p>
            </div>
            <div className="poster-slider">
              <Slider {...sliderSettings}>
                {featuredPosters.map((poster) => (
                  <div key={poster.id} className="px-2">
                    <PosterCard poster={poster} />
                  </div>
                ))}
              </Slider>
            </div>
          </div>
        </section>
      )}

      {/* Music Posters Section */}
      {musicPosters.length > 0 && (
        <section className="py-16 bg-black">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center mb-12">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Music Posters
                </h2>
                <p className="text-gray-400 text-lg">
                  Express your musical taste with iconic album artwork
                </p>
              </div>
              <Link
                to="/music-posters"
                className="text-red-400 hover:text-red-300 font-semibold flex items-center"
              >
                View All
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
            <div className="poster-slider">
              <Slider {...sliderSettings}>
                {musicPosters.map((poster) => (
                  <div key={poster.id} className="px-2">
                    <PosterCard poster={poster} />
                  </div>
                ))}
              </Slider>
            </div>
          </div>
        </section>
      )}

      {/* Movie Posters Section */}
      {moviePosters.length > 0 && (
        <section className="py-16 bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center mb-12">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Movies & Series Posters
                </h2>
                <p className="text-gray-400 text-lg">
                  Celebrate your favorite films and shows
                </p>
              </div>
              <Link
                to="/movies-posters"
                className="text-red-400 hover:text-red-300 font-semibold flex items-center"
              >
                View All
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
            <div className="poster-slider">
              <Slider {...sliderSettings}>
                {moviePosters.map((poster) => (
                  <div key={poster.id} className="px-2">
                    <PosterCard poster={poster} />
                  </div>
                ))}
              </Slider>
            </div>
          </div>
        </section>
      )}

      {/* Call to Action Section */}
      <section className="py-16 bg-red-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Space?
          </h2>
          <p className="text-red-100 text-lg mb-8">
            Browse our complete collection and find the perfect poster for your style
          </p>
          <Link
            to="/music-posters"
            className="bg-white text-red-600 hover:bg-gray-100 px-8 py-3 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105"
          >
            Start Shopping
          </Link>
        </div>
      </section>
    </div>
  );
}

export default Hero;
