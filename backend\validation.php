<?php

/**
 * Input Validation and Error Handling for I-POSTER API
 * Provides comprehensive validation functions and error responses
 */

class Validator {
    
    private $errors = [];
    
    /**
     * Validate required fields
     */
    public function required($data, $fields) {
        foreach ($fields as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $this->errors[] = "Field '$field' is required";
            }
        }
        return $this;
    }
    
    /**
     * Validate email format
     */
    public function email($email, $fieldName = 'email') {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->errors[] = "Invalid $fieldName format";
        }
        return $this;
    }
    
    /**
     * Validate phone number (Moroccan format)
     */
    public function phone($phone, $fieldName = 'phone') {
        // Moroccan phone number patterns
        $patterns = [
            '/^\+212[5-7][0-9]{8}$/',  // +212XXXXXXXXX
            '/^0[5-7][0-9]{8}$/',      // 0XXXXXXXXX
            '/^[5-7][0-9]{8}$/'        // XXXXXXXXX
        ];
        
        $isValid = false;
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $phone)) {
                $isValid = true;
                break;
            }
        }
        
        if (!$isValid) {
            $this->errors[] = "Invalid $fieldName format. Use Moroccan phone number format.";
        }
        return $this;
    }
    
    /**
     * Validate numeric values
     */
    public function numeric($value, $fieldName, $min = null, $max = null) {
        if (!is_numeric($value)) {
            $this->errors[] = "$fieldName must be a number";
            return $this;
        }
        
        $value = (float)$value;
        
        if ($min !== null && $value < $min) {
            $this->errors[] = "$fieldName must be at least $min";
        }
        
        if ($max !== null && $value > $max) {
            $this->errors[] = "$fieldName must not exceed $max";
        }
        
        return $this;
    }
    
    /**
     * Validate integer values
     */
    public function integer($value, $fieldName, $min = null, $max = null) {
        if (!is_numeric($value) || (int)$value != $value) {
            $this->errors[] = "$fieldName must be an integer";
            return $this;
        }
        
        $value = (int)$value;
        
        if ($min !== null && $value < $min) {
            $this->errors[] = "$fieldName must be at least $min";
        }
        
        if ($max !== null && $value > $max) {
            $this->errors[] = "$fieldName must not exceed $max";
        }
        
        return $this;
    }
    
    /**
     * Validate string length
     */
    public function length($value, $fieldName, $min = null, $max = null) {
        $length = strlen($value);
        
        if ($min !== null && $length < $min) {
            $this->errors[] = "$fieldName must be at least $min characters";
        }
        
        if ($max !== null && $length > $max) {
            $this->errors[] = "$fieldName must not exceed $max characters";
        }
        
        return $this;
    }
    
    /**
     * Validate enum values
     */
    public function enum($value, $fieldName, $allowedValues) {
        if (!in_array($value, $allowedValues)) {
            $allowed = implode(', ', $allowedValues);
            $this->errors[] = "$fieldName must be one of: $allowed";
        }
        return $this;
    }
    
    /**
     * Validate password strength
     */
    public function password($password, $fieldName = 'password') {
        if (strlen($password) < 8) {
            $this->errors[] = "$fieldName must be at least 8 characters long";
        }
        
        if (!preg_match('/[A-Za-z]/', $password)) {
            $this->errors[] = "$fieldName must contain at least one letter";
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $this->errors[] = "$fieldName must contain at least one number";
        }
        
        return $this;
    }
    
    /**
     * Check if validation passed
     */
    public function passes() {
        return empty($this->errors);
    }
    
    /**
     * Check if validation failed
     */
    public function fails() {
        return !empty($this->errors);
    }
    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get first error
     */
    public function getFirstError() {
        return $this->errors[0] ?? null;
    }
    
    /**
     * Clear errors
     */
    public function clearErrors() {
        $this->errors = [];
        return $this;
    }
}

/**
 * Sanitize input data
 */
class Sanitizer {
    
    /**
     * Sanitize string input
     */
    public static function string($value) {
        return htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Sanitize email input
     */
    public static function email($value) {
        return filter_var(trim($value), FILTER_SANITIZE_EMAIL);
    }
    
    /**
     * Sanitize phone number
     */
    public static function phone($value) {
        // Remove all non-numeric characters except +
        $phone = preg_replace('/[^0-9+]/', '', $value);
        
        // Normalize Moroccan phone numbers
        if (preg_match('/^0([5-7][0-9]{8})$/', $phone, $matches)) {
            return '+212' . $matches[1];
        }
        
        if (preg_match('/^([5-7][0-9]{8})$/', $phone, $matches)) {
            return '+212' . $matches[1];
        }
        
        return $phone;
    }
    
    /**
     * Sanitize numeric input
     */
    public static function numeric($value) {
        return filter_var($value, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
    }
    
    /**
     * Sanitize integer input
     */
    public static function integer($value) {
        return filter_var($value, FILTER_SANITIZE_NUMBER_INT);
    }
    
    /**
     * Sanitize array of strings
     */
    public static function stringArray($array) {
        if (!is_array($array)) {
            return [];
        }
        
        return array_map([self::class, 'string'], $array);
    }
}

/**
 * Error response helper
 */
class ErrorResponse {
    
    /**
     * Send validation error response
     */
    public static function validation($errors) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Validation failed',
            'errors' => is_array($errors) ? $errors : [$errors]
        ]);
        exit();
    }
    
    /**
     * Send not found error response
     */
    public static function notFound($message = 'Resource not found') {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
        exit();
    }
    
    /**
     * Send unauthorized error response
     */
    public static function unauthorized($message = 'Unauthorized access') {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
        exit();
    }
    
    /**
     * Send forbidden error response
     */
    public static function forbidden($message = 'Access forbidden') {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
        exit();
    }
    
    /**
     * Send method not allowed error response
     */
    public static function methodNotAllowed($message = 'Method not allowed') {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
        exit();
    }
    
    /**
     * Send server error response
     */
    public static function serverError($message = 'Internal server error') {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
        exit();
    }
    
    /**
     * Send custom error response
     */
    public static function custom($code, $message) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
        exit();
    }
}

/**
 * Success response helper
 */
class SuccessResponse {
    
    /**
     * Send success response with data
     */
    public static function data($data, $message = null) {
        $response = [
            'success' => true,
            'data' => $data
        ];
        
        if ($message) {
            $response['message'] = $message;
        }
        
        echo json_encode($response);
        exit();
    }
    
    /**
     * Send success response with message only
     */
    public static function message($message) {
        echo json_encode([
            'success' => true,
            'message' => $message
        ]);
        exit();
    }
    
    /**
     * Send paginated response
     */
    public static function paginated($data, $total, $page = 1, $perPage = 20) {
        echo json_encode([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'per_page' => $perPage,
                'total_pages' => ceil($total / $perPage)
            ]
        ]);
        exit();
    }
}

?>
