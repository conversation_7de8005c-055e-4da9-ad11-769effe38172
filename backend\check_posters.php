<?php

// Check posters table structure and data
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== POSTERS TABLE STRUCTURE ===\n";
    $stmt = $pdo->query("DESCRIBE posters");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo $row['Field'] . " - " . $row['Type'] . " - " . $row['Null'] . " - " . $row['Default'] . "\n";
    }
    
    echo "\n=== SAMPLE POSTERS DATA ===\n";
    $stmt = $pdo->query("SELECT id, title, category, status FROM posters LIMIT 5");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "ID: {$row['id']}, Title: {$row['title']}, Category: {$row['category']}, Status: {$row['status']}\n";
    }
    
    echo "\n=== MUSIC POSTERS ===\n";
    $stmt = $pdo->query("SELECT id, title, category FROM posters WHERE category = 'music'");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "ID: {$row['id']}, Title: {$row['title']}, Category: {$row['category']}\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

?>
