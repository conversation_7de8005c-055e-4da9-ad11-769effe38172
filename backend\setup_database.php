<?php

/**
 * Simple Database Setup Script for I-POSTER
 * This script creates the database tables and populates them with data
 */

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    // Connect to MySQL
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to MySQL successfully!\n";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database '$database' created/verified!\n";
    
    // Use the database
    $pdo->exec("USE `$database`");
    
    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL UNIQUE,
            `email_verified_at` timestamp NULL DEFAULT NULL,
            `password` varchar(255) NOT NULL,
            `phone` varchar(255) NULL,
            `address` text NULL,
            `role` enum('customer','admin') NOT NULL DEFAULT 'customer',
            `remember_token` varchar(100) NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Users table created!\n";
    
    // Create posters table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `posters` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text NOT NULL,
            `price` decimal(8,2) NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT 10,
            `image_path` varchar(255) NOT NULL,
            `category` enum('music','movie','art','sports','vintage') NOT NULL,
            `artist` varchar(255) NULL,
            `size` enum('A4','A3','A2','A1','50x70','70x100') NOT NULL DEFAULT 'A4',
            `frame_type` enum('none','black','white','wood','gold') NOT NULL DEFAULT 'none',
            `tags` json NULL,
            `featured` tinyint(1) NOT NULL DEFAULT 0,
            `status` enum('active','inactive','out_of_stock') NOT NULL DEFAULT 'active',
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Posters table created!\n";
    
    // Create carts table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `carts` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NULL,
            `session_id` varchar(255) NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `carts_user_id_session_id_index` (`user_id`,`session_id`),
            FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Carts table created!\n";
    
    // Create cart_items table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `cart_items` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `cart_id` bigint(20) unsigned NOT NULL,
            `poster_id` bigint(20) unsigned NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT 1,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `cart_items_cart_id_poster_id_unique` (`cart_id`,`poster_id`),
            FOREIGN KEY (`cart_id`) REFERENCES `carts` (`id`) ON DELETE CASCADE,
            FOREIGN KEY (`poster_id`) REFERENCES `posters` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Cart items table created!\n";
    
    // Create orders table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `orders` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NULL,
            `order_number` varchar(255) NOT NULL UNIQUE,
            `customer_name` varchar(255) NOT NULL,
            `customer_email` varchar(255) NULL,
            `customer_phone` varchar(255) NOT NULL,
            `delivery_address` text NOT NULL,
            `notes` text NULL,
            `total_amount` decimal(10,2) NOT NULL,
            `payment_type` varchar(255) NOT NULL DEFAULT 'Cash on Delivery',
            `status` enum('pending','confirmed','shipped','delivered','cancelled') NOT NULL DEFAULT 'pending',
            `admin_notes` text NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `orders_status_created_at_index` (`status`,`created_at`),
            KEY `orders_customer_phone_index` (`customer_phone`),
            FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Orders table created!\n";
    
    // Create order_items table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `order_items` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `order_id` bigint(20) unsigned NOT NULL,
            `poster_id` bigint(20) unsigned NOT NULL,
            `quantity` int(11) NOT NULL,
            `price` decimal(8,2) NOT NULL,
            `poster_title` varchar(255) NOT NULL,
            `poster_description` text NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
            FOREIGN KEY (`poster_id`) REFERENCES `posters` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Order items table created!\n";
    
    // Create personal_access_tokens table for Sanctum
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `personal_access_tokens` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `tokenable_type` varchar(255) NOT NULL,
            `tokenable_id` bigint(20) unsigned NOT NULL,
            `name` varchar(255) NOT NULL,
            `token` varchar(64) NOT NULL UNIQUE,
            `abilities` text NULL,
            `last_used_at` timestamp NULL DEFAULT NULL,
            `expires_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Personal access tokens table created!\n";
    
    echo "\n🎉 All tables created successfully!\n";
    echo "📊 Ready to populate with data...\n\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
