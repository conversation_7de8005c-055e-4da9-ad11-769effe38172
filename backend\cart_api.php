<?php

/**
 * Cart Management API
 * Handles cart item operations (update, remove, etc.)
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Session-ID');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Get request body for POST/PUT requests
$input = json_decode(file_get_contents('php://input'), true);

// Session management
session_start();
$sessionId = $_SERVER['HTTP_X_SESSION_ID'] ?? session_id();

// Route handling for cart item operations
if (count($pathParts) >= 3 && $pathParts[1] === 'cart' && $pathParts[2] === 'items') {
    if (isset($pathParts[3])) {
        // Operations on specific cart item
        $posterId = (int)$pathParts[3];
        handleCartItem($pdo, $method, $input, $sessionId, $posterId);
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Poster ID required']);
    }
} else {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
}

/**
 * Handle cart item operations
 */
function handleCartItem($pdo, $method, $input, $sessionId, $posterId) {
    switch ($method) {
        case 'PUT':
            updateCartItem($pdo, $input, $sessionId, $posterId);
            break;
        case 'DELETE':
            removeCartItem($pdo, $sessionId, $posterId);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
}

/**
 * Update cart item quantity
 */
function updateCartItem($pdo, $input, $sessionId, $posterId) {
    try {
        if (!isset($input['quantity']) || !is_numeric($input['quantity'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Valid quantity is required']);
            return;
        }

        $quantity = (int)$input['quantity'];

        if ($quantity < 0 || $quantity > 10) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Quantity must be between 0 and 10']);
            return;
        }

        $cartId = getOrCreateCart($pdo, $sessionId);

        // Check if item exists in cart
        $stmt = $pdo->prepare("SELECT * FROM cart_items WHERE cart_id = ? AND poster_id = ?");
        $stmt->execute([$cartId, $posterId]);
        $cartItem = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$cartItem) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Item not found in cart']);
            return;
        }

        if ($quantity === 0) {
            // Remove item if quantity is 0
            $stmt = $pdo->prepare("DELETE FROM cart_items WHERE cart_id = ? AND poster_id = ?");
            $stmt->execute([$cartId, $posterId]);
            $message = 'Item removed from cart';
        } else {
            // Update quantity
            $stmt = $pdo->prepare("UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE cart_id = ? AND poster_id = ?");
            $stmt->execute([$quantity, $cartId, $posterId]);
            $message = 'Cart item updated successfully';
        }

        // Get updated cart totals
        $cartTotals = getCartTotals($pdo, $cartId);

        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => [
                'cart_total' => $cartTotals['total'],
                'cart_count' => $cartTotals['count']
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to update cart item: ' . $e->getMessage()
        ]);
    }
}

/**
 * Remove cart item
 */
function removeCartItem($pdo, $sessionId, $posterId) {
    try {
        $cartId = getOrCreateCart($pdo, $sessionId);

        // Check if item exists in cart
        $stmt = $pdo->prepare("SELECT * FROM cart_items WHERE cart_id = ? AND poster_id = ?");
        $stmt->execute([$cartId, $posterId]);
        $cartItem = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$cartItem) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Item not found in cart']);
            return;
        }

        // Remove item
        $stmt = $pdo->prepare("DELETE FROM cart_items WHERE cart_id = ? AND poster_id = ?");
        $stmt->execute([$cartId, $posterId]);

        // Get updated cart totals
        $cartTotals = getCartTotals($pdo, $cartId);

        echo json_encode([
            'success' => true,
            'message' => 'Item removed from cart successfully',
            'data' => [
                'cart_total' => $cartTotals['total'],
                'cart_count' => $cartTotals['count']
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to remove cart item: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get cart totals
 */
function getCartTotals($pdo, $cartId) {
    $stmt = $pdo->prepare("
        SELECT 
            SUM(ci.quantity * p.price) as total,
            SUM(ci.quantity) as count
        FROM cart_items ci
        JOIN posters p ON ci.poster_id = p.id
        WHERE ci.cart_id = ?
    ");
    $stmt->execute([$cartId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'total' => round((float)($result['total'] ?? 0), 2),
        'count' => (int)($result['count'] ?? 0)
    ];
}

/**
 * Get or create cart for session
 */
function getOrCreateCart($pdo, $sessionId) {
    // Check if cart exists for this session
    $stmt = $pdo->prepare("SELECT id FROM carts WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($cart) {
        return $cart['id'];
    }

    // Create new cart
    $stmt = $pdo->prepare("INSERT INTO carts (session_id, created_at, updated_at) VALUES (?, NOW(), NOW())");
    $stmt->execute([$sessionId]);
    
    return $pdo->lastInsertId();
}

?>
