<?php

echo "Adding price column to cart_items table\n";
echo "=======================================\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=poster_shop', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Add price column
    echo "Adding price column...\n";
    $pdo->exec("ALTER TABLE cart_items ADD COLUMN price DECIMAL(10,2) NOT NULL DEFAULT 0.00 AFTER quantity");
    echo "✅ Price column added successfully!\n\n";
    
    // Update existing cart items with prices from posters table
    echo "Updating existing cart items with prices...\n";
    $stmt = $pdo->prepare("
        UPDATE cart_items ci 
        JOIN posters p ON ci.poster_id = p.id 
        SET ci.price = p.price 
        WHERE ci.price = 0.00
    ");
    $stmt->execute();
    $updated = $stmt->rowCount();
    echo "✅ Updated {$updated} cart items with prices!\n\n";
    
    // Verify the changes
    echo "Verifying changes:\n";
    $stmt = $pdo->query('DESCRIBE cart_items');
    while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "- {$row['Field']}: {$row['Type']}\n";
    }
    
    echo "\nSample cart items with prices:\n";
    $stmt = $pdo->query('SELECT ci.*, p.title FROM cart_items ci JOIN posters p ON ci.poster_id = p.id LIMIT 3');
    while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "- {$row['title']}: {$row['quantity']} x {$row['price']} MAD\n";
    }
    
} catch(Exception $e) {
    echo 'Error: ' . $e->getMessage() . "\n";
}

?>
