import React from 'react';
import { useAuth } from '../contexts/AuthContext';

function Profile() {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            👤 My Profile
          </h1>
          <p className="text-gray-400">Manage your account information</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-6">Account Information</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Name</label>
              <p className="text-white bg-gray-700 px-3 py-2 rounded-md">{user?.name}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Email</label>
              <p className="text-white bg-gray-700 px-3 py-2 rounded-md">{user?.email}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Phone</label>
              <p className="text-white bg-gray-700 px-3 py-2 rounded-md">{user?.phone || 'Not provided'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Role</label>
              <p className="text-white bg-gray-700 px-3 py-2 rounded-md capitalize">{user?.role}</p>
            </div>
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-300 mb-2">Address</label>
            <p className="text-white bg-gray-700 px-3 py-2 rounded-md">{user?.address || 'Not provided'}</p>
          </div>

          <div className="mt-8 flex gap-4">
            <button className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
              Edit Profile
            </button>
            <button className="border border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white px-6 py-2 rounded-lg transition-colors duration-200">
              Change Password
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Profile;
