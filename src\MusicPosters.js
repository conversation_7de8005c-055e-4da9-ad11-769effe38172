import React, { useState, useEffect } from "react";
// import { listposter } from "./list";
// import Slider from "react-slick";
// import 'slick-carousel/slick/slick.css';
// import 'slick-carousel/slick/slick-theme.css';
function MusicPosters() {
  const [posters, setPosters] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchMusicPosters();
  }, []);

  const fetchMusicPosters = async () => {
    try {
      const response = await fetch('http://localhost:8000/posters?category=music');
      const data = await response.json();
      setPosters(data.data || []);
    } catch (error) {
      console.error('Error fetching music posters:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px', color: 'white' }}>
        <h2>Loading music posters...</h2>
      </div>
    );
  }

  return (
    <>
      <h1 className="section-title">🎵 MUSIC POSTERS</h1>
      <section className="poster-grid" style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '20px',
        padding: '20px',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {posters.map((poster) => (
          <div className="poster-item" key={poster.id} style={{
            backgroundColor: '#2a2a2a',
            borderRadius: '10px',
            padding: '20px',
            textAlign: 'center',
            color: 'white'
          }}>
            <img
              src={poster.image_path}
              alt={poster.title}
              style={{ width: '100%', height: '200px', objectFit: 'cover', borderRadius: '8px' }}
              onError={(e) => {
                e.target.src = '/placeholder-poster.jpg';
              }}
            />
            <h3 style={{ margin: '15px 0 5px 0', color: '#e60026' }}>{poster.title}</h3>
            {poster.artist && <p style={{ margin: '5px 0', color: '#ccc' }}>by {poster.artist}</p>}
            <p style={{ margin: '10px 0', fontSize: '14px', color: '#aaa' }}>{poster.description}</p>
            <p className="price" style={{ fontSize: '18px', fontWeight: 'bold', color: '#fff', margin: '10px 0' }}>
              Prix : {poster.price} MAD
            </p>
            <div style={{ marginTop: '10px' }}>
              <span style={{ fontSize: '12px', color: '#888' }}>
                Size: {poster.size} | Frame: {poster.frame_type}
              </span>
            </div>
            {poster.tags && (
              <div style={{ margin: '10px 0' }}>
                {poster.tags.map((tag, index) => (
                  <span key={index} style={{
                    backgroundColor: '#444',
                    color: '#ccc',
                    padding: '2px 8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    margin: '2px'
                  }}>
                    {tag}
                  </span>
                ))}
              </div>
            )}
            <button className="add-to-cart" style={{
              backgroundColor: '#e60026',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '5px',
              cursor: 'pointer',
              marginTop: '10px'
            }}>
              Ajouter au panier
            </button>
          </div>
        ))}
      </section>
    </>
  );
}

export default MusicPosters;




