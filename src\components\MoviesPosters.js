import React, { useState, useEffect } from "react";
import { postersAPI } from "../services/api";
import PosterCard from "./PosterCard";

function MoviesPosters() {
  const [posters, setPosters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchMoviePosters();
  }, []);

  const fetchMoviePosters = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the correct API endpoint
      const response = await postersAPI.getAll({ category: 'movie' });
      console.log('Movie API Response:', response.data); // Debug log

      if (response.data.success && response.data.data) {
        setPosters(response.data.data);
      } else {
        setPosters([]);
        setError('No movie posters found');
      }
    } catch (error) {
      console.error('Error fetching movie posters:', error);
      setError(`Failed to load movie posters: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Error</h2>
          <p className="text-gray-400">{error}</p>
          <button
            onClick={fetchMoviePosters}
            className="mt-4 bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🎬 MOVIES & SERIES POSTERS
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Celebrate underrated cinematic gems. From mind-bending sci-fi to heartwarming comedies,
            discover films that deserve more recognition.
          </p>
        </div>

        {/* Posters Grid */}
        {posters.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {posters.map((poster) => (
              <PosterCard key={poster.id} poster={poster} />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <h3 className="text-2xl font-semibold text-white mb-4">
              No Movie Posters Found
            </h3>
            <p className="text-gray-400">
              Check back soon for new additions to our movie collection!
            </p>
          </div>
        )}

        {/* Featured Section */}
        <div className="mt-16 bg-gray-800 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-white mb-4">
            Why These Films Are Underrated
          </h2>
          <div className="grid md:grid-cols-2 gap-6 text-gray-300">
            <div>
              <h3 className="font-semibold text-white mb-2">The Fall (2006)</h3>
              <p className="text-sm">
                Tarsem Singh's visual masterpiece that combines stunning cinematography with an emotional story.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">Coherence (2013)</h3>
              <p className="text-sm">
                A mind-bending sci-fi thriller made on a tiny budget that rivals big-budget blockbusters.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">The Man from Earth</h3>
              <p className="text-sm">
                A philosophical drama that proves great storytelling doesn't need special effects.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">The Handmaiden</h3>
              <p className="text-sm">
                Park Chan-wook's psychological thriller that showcases the best of Korean cinema.
              </p>
            </div>
          </div>
        </div>

        {/* Director Spotlight */}
        <div className="mt-12 bg-red-900/20 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-white mb-4">
            🎭 Director Spotlight: Taika Waititi
          </h2>
          <p className="text-gray-300 mb-4">
            Before becoming a household name with Thor: Ragnarok, Taika Waititi created some of the most
            charming and original films in recent memory.
          </p>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="bg-gray-800 p-4 rounded-lg">
              <h3 className="font-semibold text-white">Hunt for the Wilderpeople</h3>
              <p className="text-sm text-gray-400">A heartwarming adventure through New Zealand's wilderness.</p>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <h3 className="font-semibold text-white">What We Do in the Shadows</h3>
              <p className="text-sm text-gray-400">A hilarious mockumentary about vampire flatmates.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MoviesPosters;
