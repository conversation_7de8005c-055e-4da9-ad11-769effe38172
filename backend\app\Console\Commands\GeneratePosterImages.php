<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class GeneratePosterImages extends Command
{
    protected $signature = 'posters:generate-images';
    protected $description = 'Generate placeholder images for posters';

    public function handle()
    {
        $this->info('Generating poster placeholder images...');
        
        // Create storage directory if it doesn't exist
        if (!Storage::disk('public')->exists('posters')) {
            Storage::disk('public')->makeDirectory('posters');
        }
        
        // Generate music poster placeholders
        for ($i = 1; $i <= 5; $i++) {
            $this->generateImage(
                "Music Poster $i", 
                "music_$i.jpg", 
                [255, 100, 100] // Reddish background for music
            );
        }
        
        // Generate movie poster placeholders
        for ($i = 1; $i <= 5; $i++) {
            $this->generateImage(
                "Movie Poster $i", 
                "movie_$i.jpg", 
                [100, 100, 255] // Bluish background for movies
            );
        }
        
        $this->info('Poster images generated successfully!');
        return Command::SUCCESS;
    }
    
    private function generateImage($text, $filename, $bgColor)
    {
        // Create a 600x900 image (typical poster ratio)
        $image = imagecreatetruecolor(600, 900);
        
        // Set background color
        $bg = imagecolorallocate($image, $bgColor[0], $bgColor[1], $bgColor[2]);
        imagefill($image, 0, 0, $bg);
        
        // Add text
        $textColor = imagecolorallocate($image, 255, 255, 255);
        $font = 5; // Built-in font
        
        // Center the text
        $textWidth = imagefontwidth($font) * strlen($text);
        $textHeight = imagefontheight($font);
        $centerX = (600 - $textWidth) / 2;
        $centerY = (900 - $textHeight) / 2;
        
        imagestring($image, $font, $centerX, $centerY, $text, $textColor);
        
        // Save the image
        ob_start();
        imagejpeg($image, null, 90);
        $imageData = ob_get_clean();
        
        Storage::disk('public')->put('posters/' . $filename, $imageData);
        
        // Free memory
        imagedestroy($image);
    }
}