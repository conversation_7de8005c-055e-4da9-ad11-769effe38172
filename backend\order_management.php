<?php

/**
 * Order Management API for I-POSTER
 * Handles order tracking, status updates, and customer order lookup
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Session-ID');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/backend/', '', $path);
$path = trim($path, '/');

// Get request body for POST/PUT requests
$input = json_decode(file_get_contents('php://input'), true);

// Route handling
switch ($path) {
    case 'orders/track':
        trackOrder($pdo, $method);
        break;
    
    case 'orders/customer':
        getCustomerOrders($pdo, $method);
        break;
    
    case 'orders/status':
        updateOrderStatus($pdo, $method, $input);
        break;
    
    default:
        if (preg_match('/^orders\/(\d+)$/', $path, $matches)) {
            getOrderDetails($pdo, $method, $matches[1]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
        }
        break;
}

/**
 * Track order by order number
 */
function trackOrder($pdo, $method) {
    if ($method !== 'GET') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        $orderNumber = $_GET['order_number'] ?? null;
        $phone = $_GET['phone'] ?? null;

        if (!$orderNumber) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Order number is required']);
            return;
        }

        $sql = "SELECT * FROM orders WHERE order_number = ?";
        $params = [$orderNumber];

        // Optional phone verification for security
        if ($phone) {
            $sql .= " AND customer_phone = ?";
            $params[] = $phone;
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$order) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Order not found']);
            return;
        }

        // Get order items
        $stmt = $pdo->prepare("SELECT * FROM order_items WHERE order_id = ?");
        $stmt->execute([$order['id']]);
        $order['items'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Format order data
        $order['total_amount'] = (float)$order['total_amount'];
        $order['status_info'] = getOrderStatusInfo($order['status']);

        echo json_encode([
            'success' => true,
            'data' => $order
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to track order: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get customer orders by phone or email
 */
function getCustomerOrders($pdo, $method) {
    if ($method !== 'GET') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        $phone = $_GET['phone'] ?? null;
        $email = $_GET['email'] ?? null;

        if (!$phone && !$email) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Phone number or email is required']);
            return;
        }

        $sql = "SELECT * FROM orders WHERE ";
        $params = [];

        if ($phone) {
            $sql .= "customer_phone = ?";
            $params[] = $phone;
        }

        if ($email) {
            if ($phone) $sql .= " OR ";
            $sql .= "customer_email = ?";
            $params[] = $email;
        }

        $sql .= " ORDER BY created_at DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get order items for each order
        foreach ($orders as &$order) {
            $stmt = $pdo->prepare("SELECT * FROM order_items WHERE order_id = ?");
            $stmt->execute([$order['id']]);
            $order['items'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $order['total_amount'] = (float)$order['total_amount'];
            $order['status_info'] = getOrderStatusInfo($order['status']);
        }

        echo json_encode([
            'success' => true,
            'data' => $orders,
            'total' => count($orders)
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to fetch customer orders: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get detailed order information
 */
function getOrderDetails($pdo, $method, $orderId) {
    if ($method !== 'GET') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
        $stmt->execute([$orderId]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$order) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Order not found']);
            return;
        }

        // Get order items with poster details
        $stmt = $pdo->prepare("
            SELECT oi.*, p.image_path, p.category, p.artist, p.size
            FROM order_items oi
            LEFT JOIN posters p ON oi.poster_id = p.id
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$orderId]);
        $order['items'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Format order data
        $order['total_amount'] = (float)$order['total_amount'];
        $order['status_info'] = getOrderStatusInfo($order['status']);

        echo json_encode([
            'success' => true,
            'data' => $order
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to fetch order details: ' . $e->getMessage()
        ]);
    }
}

/**
 * Update order status (admin only)
 */
function updateOrderStatus($pdo, $method, $input) {
    if ($method !== 'PUT') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        // Simple admin check (in production, use proper authentication)
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (!isValidAdminToken($pdo, $authHeader)) {
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
            return;
        }

        if (!isset($input['order_id']) || !isset($input['status'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Order ID and status are required']);
            return;
        }

        $validStatuses = ['pending', 'confirmed', 'shipped', 'delivered', 'cancelled'];
        if (!in_array($input['status'], $validStatuses)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid status']);
            return;
        }

        $stmt = $pdo->prepare("UPDATE orders SET status = ?, admin_notes = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([
            $input['status'],
            $input['admin_notes'] ?? null,
            $input['order_id']
        ]);

        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Order not found']);
            return;
        }

        echo json_encode([
            'success' => true,
            'message' => 'Order status updated successfully',
            'data' => [
                'order_id' => $input['order_id'],
                'new_status' => $input['status'],
                'status_info' => getOrderStatusInfo($input['status'])
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to update order status: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get order status information
 */
function getOrderStatusInfo($status) {
    $statusInfo = [
        'pending' => [
            'label' => 'Order Pending',
            'description' => 'Your order has been received and is being processed.',
            'color' => 'orange',
            'icon' => '⏳'
        ],
        'confirmed' => [
            'label' => 'Order Confirmed',
            'description' => 'Your order has been confirmed and is being prepared.',
            'color' => 'blue',
            'icon' => '✅'
        ],
        'shipped' => [
            'label' => 'Order Shipped',
            'description' => 'Your order has been shipped and is on its way.',
            'color' => 'purple',
            'icon' => '🚚'
        ],
        'delivered' => [
            'label' => 'Order Delivered',
            'description' => 'Your order has been successfully delivered.',
            'color' => 'green',
            'icon' => '📦'
        ],
        'cancelled' => [
            'label' => 'Order Cancelled',
            'description' => 'Your order has been cancelled.',
            'color' => 'red',
            'icon' => '❌'
        ]
    ];

    return $statusInfo[$status] ?? [
        'label' => 'Unknown Status',
        'description' => 'Status information not available.',
        'color' => 'gray',
        'icon' => '❓'
    ];
}

/**
 * Check if admin token is valid
 */
function isValidAdminToken($pdo, $token) {
    if (empty($token)) return false;
    
    // Remove "Bearer " prefix if present
    $token = str_replace('Bearer ', '', $token);
    
    $stmt = $pdo->prepare("SELECT role FROM users WHERE remember_token = ?");
    $stmt->execute([$token]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return $user && $user['role'] === 'admin';
}

?>
