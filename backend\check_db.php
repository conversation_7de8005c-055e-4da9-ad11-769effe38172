<?php

echo "Checking Database Structure\n";
echo "===========================\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=poster_shop', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Cart Items Table Structure:\n";
    $stmt = $pdo->query('DESCRIBE cart_items');
    while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "- {$row['Field']}: {$row['Type']} ({$row['Null']}, {$row['Key']}, {$row['Default']})\n";
    }
    
    echo "\nCurrent Cart Items:\n";
    $stmt = $pdo->query('SELECT * FROM cart_items LIMIT 5');
    while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        print_r($row);
    }
    
} catch(Exception $e) {
    echo 'Error: ' . $e->getMessage() . "\n";
}

?>
