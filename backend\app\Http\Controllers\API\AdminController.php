<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Poster;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    public function dashboard()
    {
        $stats = [
            'total_orders' => Order::count(),
            'pending_orders' => Order::pending()->count(),
            'confirmed_orders' => Order::confirmed()->count(),
            'shipped_orders' => Order::shipped()->count(),
            'delivered_orders' => Order::delivered()->count(),
            'total_revenue' => Order::delivered()->sum('total_amount'),
            'total_posters' => Poster::count(),
            'active_posters' => Poster::where('status', 'active')->count(),
            'out_of_stock_posters' => Poster::where('quantity', 0)->count(),
            'total_customers' => User::customers()->count(),
        ];

        // Recent orders
        $recent_orders = Order::with(['items.poster'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Top selling posters
        $top_posters = DB::table('order_items')
            ->select('poster_id', 'poster_title', DB::raw('SUM(quantity) as total_sold'))
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.status', '!=', 'cancelled')
            ->groupBy('poster_id', 'poster_title')
            ->orderBy('total_sold', 'desc')
            ->limit(10)
            ->get();

        // Orders by status for chart
        $orders_by_status = Order::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        // Revenue by month (last 12 months)
        $revenue_by_month = Order::select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(total_amount) as revenue')
            )
            ->where('status', 'delivered')
            ->where('created_at', '>=', now()->subMonths(12))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        return response()->json([
            'stats' => $stats,
            'recent_orders' => $recent_orders,
            'top_posters' => $top_posters,
            'orders_by_status' => $orders_by_status,
            'revenue_by_month' => $revenue_by_month
        ]);
    }

    public function users(Request $request)
    {
        $query = User::query();

        if ($request->has('role')) {
            $query->where('role', $request->role);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate(20);
    }

    public function updateUserRole(Request $request, $userId)
    {
        $validated = $request->validate([
            'role' => 'required|in:customer,admin'
        ]);

        $user = User::findOrFail($userId);
        $user->update(['role' => $validated['role']]);

        return response()->json([
            'message' => 'User role updated successfully',
            'user' => $user
        ]);
    }
}
