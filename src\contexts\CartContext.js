import React, { createContext, useContext, useState, useEffect } from 'react';
import { cartAPI } from '../services/api';

const CartContext = createContext();

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export const CartProvider = ({ children }) => {
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(false);
  const [totalPrice, setTotalPrice] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const response = await cartAPI.get();
      setCart(response.data.cart);
      setTotalPrice(response.data.total_price);
      setTotalItems(response.data.total_items);
    } catch (error) {
      console.error('Error fetching cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (posterId, quantity = 1) => {
    try {
      const response = await cartAPI.addItem(posterId, quantity);
      setTotalPrice(response.data.cart_total);
      setTotalItems(response.data.cart_items_count);
      await fetchCart(); // Refresh cart data
      return { success: true, message: response.data.message };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to add item to cart' 
      };
    }
  };

  const updateCartItem = async (posterId, quantity) => {
    try {
      const response = await cartAPI.updateItem(posterId, quantity);
      setTotalPrice(response.data.cart_total);
      setTotalItems(response.data.cart_items_count);
      await fetchCart(); // Refresh cart data
      return { success: true, message: response.data.message };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to update cart item' 
      };
    }
  };

  const removeFromCart = async (posterId) => {
    try {
      const response = await cartAPI.removeItem(posterId);
      setTotalPrice(response.data.cart_total);
      setTotalItems(response.data.cart_items_count);
      await fetchCart(); // Refresh cart data
      return { success: true, message: response.data.message };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to remove item from cart' 
      };
    }
  };

  const clearCart = async () => {
    try {
      await cartAPI.clear();
      setCart(null);
      setTotalPrice(0);
      setTotalItems(0);
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to clear cart' 
      };
    }
  };

  const getCartItemQuantity = (posterId) => {
    if (!cart?.items) return 0;
    const item = cart.items.find(item => item.poster_id === posterId);
    return item ? item.quantity : 0;
  };

  const isInCart = (posterId) => {
    return getCartItemQuantity(posterId) > 0;
  };

  const value = {
    cart,
    loading,
    totalPrice,
    totalItems,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    getCartItemQuantity,
    isInCart,
    fetchCart,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
