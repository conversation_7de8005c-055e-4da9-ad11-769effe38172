<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('posters', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->decimal('price', 8, 2);
            $table->integer('quantity')->default(10);
            $table->string('image_path');
            $table->enum('category', ['music', 'movie', 'art', 'sports', 'vintage']);
            $table->string('artist')->nullable();
            $table->enum('size', ['A4', 'A3', 'A2', 'A1', '50x70', '70x100'])->default('A4');
            $table->enum('frame_type', ['none', 'black', 'white', 'wood', 'gold'])->default('none');
            $table->json('tags')->nullable();
            $table->boolean('featured')->default(false);
            $table->enum('status', ['active', 'inactive', 'out_of_stock'])->default('active');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('posters');
    }
};