<?php

/**
 * Add Movie Posters Script for I-POSTER
 * This script adds underrated movie posters to the database
 */

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database successfully!\n";
    
    // UNDERRATED MOVIE POSTERS
    $moviePosters = [
        [
            'title' => 'The Fall',
            'description' => 'The Fall (2006) - Tarsem Singh',
            'price' => 130.00,
            'quantity' => 8,
            'image_path' => '/storage/posters/the_fall.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A2',
            'frame_type' => 'gold',
            'tags' => '["fantasy", "visual-masterpiece", "underrated", "artistic"]',
            'featured' => 1
        ],
        [
            'title' => 'Coherence',
            'description' => 'Coherence (2013) - <PERSON>',
            'price' => 95.00,
            'quantity' => 12,
            'image_path' => '/storage/posters/coherence.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'black',
            'tags' => '["sci-fi", "thriller", "mind-bending", "indie"]',
            'featured' => 1
        ],
        [
            'title' => 'The Man from Earth',
            'description' => 'The Man from Earth (2007) - Richard Schenkman',
            'price' => 80.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/man_from_earth.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'none',
            'tags' => '["drama", "philosophical", "low-budget-gem", "thought-provoking"]',
            'featured' => 0
        ],
        [
            'title' => 'Hunt for the Wilderpeople',
            'description' => 'Hunt for the Wilderpeople (2016) - Taika Waititi',
            'price' => 100.00,
            'quantity' => 15,
            'image_path' => '/storage/posters/hunt_wilderpeople.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'wood',
            'tags' => '["comedy", "adventure", "new-zealand", "heartwarming"]',
            'featured' => 0
        ],
        [
            'title' => 'The Handmaiden',
            'description' => 'The Handmaiden (2016) - Park Chan-wook',
            'price' => 140.00,
            'quantity' => 7,
            'image_path' => '/storage/posters/handmaiden.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A2',
            'frame_type' => 'black',
            'tags' => '["thriller", "korean", "psychological", "artistic"]',
            'featured' => 1
        ],
        [
            'title' => 'What We Do in the Shadows',
            'description' => 'What We Do in the Shadows (2014) - Taika Waititi',
            'price' => 85.00,
            'quantity' => 20,
            'image_path' => '/storage/posters/what_we_do_shadows.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'none',
            'tags' => '["comedy", "horror", "mockumentary", "cult"]',
            'featured' => 0
        ],
        [
            'title' => 'The Nice Guys',
            'description' => 'The Nice Guys (2016) - Shane Black',
            'price' => 90.00,
            'quantity' => 11,
            'image_path' => '/storage/posters/nice_guys.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'white',
            'tags' => '["comedy", "crime", "neo-noir", "70s-style"]',
            'featured' => 0
        ],
        [
            'title' => 'Annihilation',
            'description' => 'Annihilation (2018) - Alex Garland',
            'price' => 125.00,
            'quantity' => 9,
            'image_path' => '/storage/posters/annihilation.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A2',
            'frame_type' => 'black',
            'tags' => '["sci-fi", "horror", "psychological", "visually-stunning"]',
            'featured' => 1
        ]
    ];
    
    foreach ($moviePosters as $poster) {
        $stmt = $pdo->prepare("
            INSERT INTO posters (title, description, price, quantity, image_path, category, artist, size, frame_type, tags, featured, status, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
        ");
        $stmt->execute([
            $poster['title'],
            $poster['description'],
            $poster['price'],
            $poster['quantity'],
            $poster['image_path'],
            $poster['category'],
            $poster['artist'],
            $poster['size'],
            $poster['frame_type'],
            $poster['tags'],
            $poster['featured']
        ]);
    }
    echo "✅ Movie posters added!\n";
    
    echo "\n🎬 Added " . count($moviePosters) . " underrated movie posters\n";
    echo "🎉 Database now contains both music and movie posters!\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
