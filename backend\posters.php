<?php

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

/**
 * Posters API Endpoint
 * Simple endpoint for fetching posters
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Session-ID');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Only handle GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit();
}

try {
    // Get query parameters
    $category = $_GET['category'] ?? null;
    $featured = $_GET['featured'] ?? null;

    // Build simple query
    $sql = "SELECT * FROM posters WHERE status = 'active'";
    $params = [];

    if ($category) {
        $sql .= " AND category = ?";
        $params[] = $category;
    }

    if ($featured) {
        $sql .= " AND featured = 1";
    }

    $sql .= " ORDER BY id ASC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $posters = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Convert fields
    foreach ($posters as &$poster) {
        if (isset($poster['tags']) && $poster['tags']) {
            $poster['tags'] = json_decode($poster['tags'], true);
        }
        $poster['featured'] = (bool)$poster['featured'];
        $poster['price'] = (float)$poster['price'];
    }

    $response = [
        'success' => true,
        'data' => $posters,
        'total' => count($posters)
    ];

    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch posters: ' . $e->getMessage()
    ]);
}

?>
