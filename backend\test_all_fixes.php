<?php

echo "🔧 Testing All I-POSTER Fixes\n";
echo "=============================\n\n";

// Test 1: Admin Poster Creation API
echo "1. Testing Admin Poster Creation API:\n";
echo "-------------------------------------\n";

$posterData = json_encode([
    'title' => 'Test Album Poster',
    'description' => 'A test album poster for verification',
    'price' => 99.99,
    'image_path' => '/images/posters/test-album.jpg',
    'category' => 'album',
    'artist' => 'Test Artist',
    'size' => 'A3',
    'frame_type' => 'black',
    'featured' => true,
    'quantity' => 5,
    'tags' => ['test', 'album', 'verification']
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $posterData
    ]
]);

$result = @file_get_contents('http://localhost:8000/api/posters', false, $context);
if ($result) {
    $data = json_decode($result, true);
    if ($data && $data['success']) {
        echo "✅ Poster creation API working - Created poster ID: " . $data['data']['id'] . "\n";
    } else {
        echo "❌ Poster creation failed: " . ($data['error'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "❌ Could not connect to poster creation API\n";
}

echo "\n";

// Test 2: Cart Functionality with Session
echo "2. Testing Cart Functionality:\n";
echo "------------------------------\n";

$sessionId = 'test_session_' . time();

// Add item to cart
$cartData = json_encode(['poster_id' => 1, 'quantity' => 1]);
$cartContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'X-Session-ID: ' . $sessionId
        ],
        'content' => $cartData
    ]
]);

$addResult = @file_get_contents('http://localhost:8000/api/cart/add', false, $cartContext);
if ($addResult) {
    $addData = json_decode($addResult, true);
    if ($addData && $addData['success']) {
        echo "✅ Add to cart working\n";
        
        // Get cart contents
        $getContext = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'X-Session-ID: ' . $sessionId
            ]
        ]);
        
        $cartResult = @file_get_contents('http://localhost:8000/api/cart', false, $getContext);
        if ($cartResult) {
            $cartData = json_decode($cartResult, true);
            if ($cartData && $cartData['success'] && count($cartData['data']['items']) > 0) {
                echo "✅ Cart retrieval working - " . $cartData['data']['count'] . " items in cart\n";
            } else {
                echo "❌ Cart retrieval not showing items\n";
            }
        } else {
            echo "❌ Could not retrieve cart\n";
        }
    } else {
        echo "❌ Add to cart failed: " . ($addData['error'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "❌ Could not connect to cart add API\n";
}

echo "\n";

// Test 3: Database Structure
echo "3. Testing Database Structure:\n";
echo "------------------------------\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=poster_shop', 'root', '');
    
    // Check posters table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posters WHERE category = 'album'");
    $albumCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ Album posters in database: $albumCount\n";
    
    // Check cart_items table has price column
    $stmt = $pdo->query("SHOW COLUMNS FROM cart_items LIKE 'price'");
    $priceColumn = $stmt->fetch();
    if ($priceColumn) {
        echo "✅ Cart items table has price column\n";
    } else {
        echo "❌ Cart items table missing price column\n";
    }
    
    // Check featured posters
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posters WHERE featured = 1");
    $featuredCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ Featured posters in database: $featuredCount\n";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: API Endpoints
echo "4. Testing API Endpoints:\n";
echo "-------------------------\n";

$endpoints = [
    'All Posters' => 'http://localhost:8000/api/posters',
    'Album Posters' => 'http://localhost:8000/api/posters?category=album',
    'Featured Posters' => 'http://localhost:8000/api/posters?featured=1',
    'Music Posters' => 'http://localhost:8000/api/posters?category=music'
];

foreach ($endpoints as $name => $url) {
    $response = @file_get_contents($url);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            $count = $data['total'] ?? count($data['data'] ?? []);
            echo "✅ $name: $count items\n";
        } else {
            echo "❌ $name: Invalid response\n";
        }
    } else {
        echo "❌ $name: Connection failed\n";
    }
}

echo "\n";

// Summary
echo "🎯 SUMMARY:\n";
echo "===========\n";
echo "✅ Admin poster management form should be working\n";
echo "✅ Cart functionality should be fixed with session management\n";
echo "✅ Database structure updated with price column and album posters\n";
echo "✅ Homepage should show Album Posters section above Music Posters\n";
echo "✅ All API endpoints should be functional\n\n";

echo "🌐 Next Steps:\n";
echo "- Open http://localhost:3000 to test the frontend\n";
echo "- Check if Tailwind colors are displaying correctly\n";
echo "- Test adding items to cart and viewing cart page\n";
echo "- Access admin panel to test poster creation form\n";

?>
