import React, { useState } from 'react';
import { useCart } from '../contexts/CartContext';

function PosterCard({ poster }) {
  const { addToCart, isInCart, getCartItemQuantity } = useCart();
  const [loading, setLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleAddToCart = async () => {
    setLoading(true);
    const result = await addToCart(poster.id, 1);

    if (result.success) {
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
    } else {
      alert(result.error);
    }

    setLoading(false);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(price);
  };

  const inCart = isInCart(poster.id);
  const cartQuantity = getCartItemQuantity(poster.id);

  return (
    <div className="bg-secondary-dark rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer border border-neutral-medium/10">
      {/* Image Container */}
      <div className="relative overflow-hidden">
        <img
          src={poster.image_path}
          alt={poster.title}
          className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
          onError={(e) => {
            e.target.src = '/placeholder-poster.jpg'; // Fallback image
          }}
        />

        {/* Featured Badge */}
        {poster.featured && (
          <div className="absolute top-2 left-2 bg-accent-red text-pure-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
            ⭐ Featured
          </div>
        )}

        {/* Quick Add Button */}
        <div className="absolute inset-0 bg-primary-dark bg-opacity-70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <button
            onClick={handleAddToCart}
            disabled={loading || poster.quantity === 0}
            className={`px-6 py-2 rounded-lg font-semibold transition-all duration-200 shadow-lg ${
              poster.quantity === 0
                ? 'bg-neutral-medium text-neutral-light cursor-not-allowed'
                : loading
                ? 'bg-accent-red-medium text-pure-white cursor-wait'
                : 'bg-accent-red hover:bg-accent-red-dark text-pure-white transform hover:scale-105'
            }`}
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Adding...
              </div>
            ) : poster.quantity === 0 ? (
              'Out of Stock'
            ) : (
              'Quick Add'
            )}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Title and Artist */}
        <div className="mb-2">
          <h3 className="text-pure-white font-semibold text-lg truncate">
            {poster.title}
          </h3>
          {poster.artist && (
            <p className="text-neutral-medium text-sm">
              by {poster.artist}
            </p>
          )}
        </div>

        {/* Description */}
        <p className="text-neutral-light text-sm mb-3 line-clamp-2">
          {poster.description}
        </p>

        {/* Tags */}
        {poster.tags && poster.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {poster.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="bg-primary-dark text-neutral-light px-2 py-1 rounded-full text-xs border border-neutral-medium/20"
              >
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* Size and Frame */}
        <div className="flex justify-between text-xs text-neutral-medium mb-3">
          <span>Size: {poster.size}</span>
          <span>Frame: {poster.frame_type === 'none' ? 'No Frame' : poster.frame_type}</span>
        </div>

        {/* Price and Actions */}
        <div className="flex items-center justify-between">
          <div>
            <span className="text-pure-white font-bold text-lg">
              {formatPrice(poster.price)}
            </span>
            {poster.quantity <= 5 && poster.quantity > 0 && (
              <p className="text-accent-red-light text-xs font-medium">
                Only {poster.quantity} left!
              </p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {inCart && (
              <span className="bg-accent-red text-pure-white px-3 py-1 rounded-full text-xs font-bold">
                In Cart ({cartQuantity})
              </span>
            )}

            <button
              onClick={handleAddToCart}
              disabled={loading || poster.quantity === 0}
              className={`px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 shadow-md hover:shadow-lg ${
                poster.quantity === 0
                  ? 'bg-neutral-medium text-neutral-light cursor-not-allowed'
                  : loading
                  ? 'bg-accent-red-medium text-pure-white cursor-wait'
                  : 'bg-accent-red hover:bg-accent-red-dark text-pure-white transform hover:scale-105'
              }`}
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : poster.quantity === 0 ? (
                'Out of Stock'
              ) : (
                'Add to Cart'
              )}
            </button>
          </div>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="mt-2 p-2 bg-accent-red text-pure-white text-sm rounded-lg text-center animate-fade-in font-medium">
            ✓ Added to cart successfully!
          </div>
        )}
      </div>
    </div>
  );
}

export default PosterCard;
