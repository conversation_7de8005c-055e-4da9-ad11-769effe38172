import React, { useState } from 'react';
import { useCart } from '../contexts/CartContext';

function PosterCard({ poster }) {
  const { addToCart, isInCart, getCartItemQuantity } = useCart();
  const [loading, setLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleAddToCart = async () => {
    setLoading(true);
    const result = await addToCart(poster.id, 1);

    if (result.success) {
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
    } else {
      alert(result.error);
    }

    setLoading(false);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(price);
  };

  const inCart = isInCart(poster.id);
  const cartQuantity = getCartItemQuantity(poster.id);

  return (
    <div style={{
      backgroundColor: '#2a2a2a',
      borderRadius: '10px',
      overflow: 'hidden',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      transition: 'all 0.3s ease',
      transform: 'scale(1)',
      cursor: 'pointer'
    }}
    onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
    onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}>
      {/* Image Container */}
      <div className="relative overflow-hidden">
        <img
          src={poster.image_path}
          alt={poster.title}
          className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
          onError={(e) => {
            e.target.src = '/placeholder-poster.jpg'; // Fallback image
          }}
        />

        {/* Featured Badge */}
        {poster.featured && (
          <div className="absolute top-2 left-2 bg-red-600 text-white px-2 py-1 rounded-md text-xs font-semibold">
            Featured
          </div>
        )}

        {/* Quick Add Button */}
        <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <button
            onClick={handleAddToCart}
            disabled={loading || poster.quantity === 0}
            className={`px-6 py-2 rounded-lg font-semibold transition-all duration-200 ${
              poster.quantity === 0
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : loading
                ? 'bg-red-400 text-white cursor-wait'
                : 'bg-red-600 hover:bg-red-700 text-white transform hover:scale-105'
            }`}
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Adding...
              </div>
            ) : poster.quantity === 0 ? (
              'Out of Stock'
            ) : (
              'Quick Add'
            )}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Title and Artist */}
        <div className="mb-2">
          <h3 className="text-white font-semibold text-lg truncate">
            {poster.title}
          </h3>
          {poster.artist && (
            <p className="text-gray-400 text-sm">
              by {poster.artist}
            </p>
          )}
        </div>

        {/* Description */}
        <p className="text-gray-300 text-sm mb-3 line-clamp-2">
          {poster.description}
        </p>

        {/* Tags */}
        {poster.tags && poster.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {poster.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="bg-gray-700 text-gray-300 px-2 py-1 rounded-md text-xs"
              >
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* Size and Frame */}
        <div className="flex justify-between text-xs text-gray-400 mb-3">
          <span>Size: {poster.size}</span>
          <span>Frame: {poster.frame_type === 'none' ? 'No Frame' : poster.frame_type}</span>
        </div>

        {/* Price and Actions */}
        <div className="flex items-center justify-between">
          <div>
            <span className="text-white font-bold text-lg">
              {formatPrice(poster.price)}
            </span>
            {poster.quantity <= 5 && poster.quantity > 0 && (
              <p className="text-yellow-400 text-xs">
                Only {poster.quantity} left!
              </p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {inCart && (
              <span className="bg-green-600 text-white px-2 py-1 rounded-md text-xs">
                In Cart ({cartQuantity})
              </span>
            )}

            <button
              onClick={handleAddToCart}
              disabled={loading || poster.quantity === 0}
              className={`px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 ${
                poster.quantity === 0
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : loading
                  ? 'bg-red-400 text-white cursor-wait'
                  : 'bg-red-600 hover:bg-red-700 text-white'
              }`}
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : poster.quantity === 0 ? (
                'Out of Stock'
              ) : (
                'Add to Cart'
              )}
            </button>
          </div>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="mt-2 p-2 bg-green-600 text-white text-sm rounded-md text-center animate-fade-in">
            Added to cart successfully!
          </div>
        )}
      </div>
    </div>
  );
}

export default PosterCard;
