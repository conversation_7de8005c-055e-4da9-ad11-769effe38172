import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useCart } from "../contexts/CartContext";
import logo from "../posters image/Capture_d_écran_16-12-2024_182813_looka.com-removebg-preview.png";

function Header() {
  const { isAuthenticated, user, logout, isAdmin } = useAuth();
  const { totalItems } = useCart();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  return (
    <header className="fixed top-0 w-full z-50 bg-primary-dark/95 backdrop-blur-sm border-b border-secondary-dark">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex-shrink-0">
            <img className="h-12 w-auto" src={logo} alt="I-POSTER" />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className="text-neutral-light hover:text-accent-red-light transition-colors duration-200 font-medium"
            >
              Accueil
            </Link>

            {/* Catalogue Dropdown */}
            <div className="relative group">
              <button className="text-neutral-light hover:text-accent-red-light transition-colors duration-200 flex items-center font-medium">
                Catalogue
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-56 bg-secondary-dark/95 backdrop-blur-sm rounded-lg shadow-xl border border-neutral-medium/20 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <Link
                  to="/featured-posters"
                  className="block px-4 py-3 text-sm text-neutral-light hover:bg-accent-red hover:text-pure-white transition-colors duration-200 border-b border-neutral-medium/20 rounded-t-lg"
                >
                  ⭐ Featured Posters
                </Link>
                <Link
                  to="/music-posters"
                  className="block px-4 py-3 text-sm text-neutral-light hover:bg-accent-red hover:text-pure-white transition-colors duration-200"
                >
                  🎵 Music Posters
                </Link>
                <Link
                  to="/album-posters"
                  className="block px-4 py-3 text-sm text-neutral-light hover:bg-accent-red hover:text-pure-white transition-colors duration-200"
                >
                  💿 Album Posters
                </Link>
                <Link
                  to="/movies-posters"
                  className="block px-4 py-3 text-sm text-neutral-light hover:bg-accent-red hover:text-pure-white transition-colors duration-200 rounded-b-lg"
                >
                  🎬 Movies & Series
                </Link>
              </div>
            </div>

            <Link
              to="/about"
              className="text-neutral-light hover:text-accent-red-light transition-colors duration-200 font-medium"
            >
              About Us
            </Link>
          </nav>

          {/* Right side - Cart, Auth, Admin */}
          <div className="flex items-center space-x-4">
            {/* Cart */}
            <Link
              to="/cart"
              className="relative text-neutral-light hover:text-accent-red-light transition-colors duration-200 p-2 rounded-lg hover:bg-secondary-dark"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6" />
              </svg>
              {totalItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-accent-red text-pure-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                  {totalItems}
                </span>
              )}
            </Link>

            {/* Authentication */}
            {isAuthenticated ? (
              <div className="relative group">
                <button className="text-neutral-light hover:text-accent-red-light transition-colors duration-200 flex items-center p-2 rounded-lg hover:bg-secondary-dark">
                  <svg className="h-6 w-6 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span className="font-medium">{user?.name}</span>
                </button>
                <div className="absolute right-0 mt-2 w-48 bg-secondary-dark/95 backdrop-blur-sm rounded-lg shadow-xl border border-neutral-medium/20 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Link
                    to="/profile"
                    className="block px-4 py-3 text-sm text-neutral-light hover:bg-accent-red hover:text-pure-white transition-colors duration-200 rounded-t-lg"
                  >
                    Profile
                  </Link>
                  <Link
                    to="/orders"
                    className="block px-4 py-3 text-sm text-neutral-light hover:bg-accent-red hover:text-pure-white transition-colors duration-200"
                  >
                    My Orders
                  </Link>
                  {isAdmin() && (
                    <Link
                      to="/admin"
                      className="block px-4 py-3 text-sm text-neutral-light hover:bg-accent-red hover:text-pure-white transition-colors duration-200 border-t border-neutral-medium/20"
                    >
                      Admin Dashboard
                    </Link>
                  )}
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-3 text-sm text-neutral-light hover:bg-accent-red-dark hover:text-pure-white transition-colors duration-200 rounded-b-lg border-t border-neutral-medium/20"
                  >
                    Logout
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link
                  to="/login"
                  className="text-neutral-light hover:text-accent-red-light transition-colors duration-200 font-medium px-3 py-2 rounded-lg hover:bg-secondary-dark"
                >
                  Login
                </Link>
                <Link
                  to="/signup"
                  className="bg-accent-red hover:bg-accent-red-dark text-pure-white px-4 py-2 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  Sign Up
                </Link>
              </div>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden text-neutral-light hover:text-accent-red-light transition-colors duration-200 p-2 rounded-lg hover:bg-secondary-dark"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-secondary-dark/95 backdrop-blur-sm rounded-lg mt-2 border border-neutral-medium/20">
              <Link
                to="/"
                className="block px-4 py-3 text-neutral-light hover:bg-accent-red hover:text-pure-white rounded-lg transition-colors duration-200 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Accueil
              </Link>
              <Link
                to="/featured-posters"
                className="block px-4 py-3 text-neutral-light hover:bg-accent-red hover:text-pure-white rounded-lg transition-colors duration-200 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                ⭐ Featured Posters
              </Link>
              <Link
                to="/music-posters"
                className="block px-4 py-3 text-neutral-light hover:bg-accent-red hover:text-pure-white rounded-lg transition-colors duration-200 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                🎵 Music Posters
              </Link>
              <Link
                to="/album-posters"
                className="block px-4 py-3 text-neutral-light hover:bg-accent-red hover:text-pure-white rounded-lg transition-colors duration-200 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                💿 Album Posters
              </Link>
              <Link
                to="/movies-posters"
                className="block px-4 py-3 text-neutral-light hover:bg-accent-red hover:text-pure-white rounded-lg transition-colors duration-200 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                🎬 Movies & Series
              </Link>
              <Link
                to="/about"
                className="block px-3 py-2 text-white hover:bg-red-600 rounded-md transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                About Us
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}

export default Header;
