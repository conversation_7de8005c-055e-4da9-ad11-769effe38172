import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useCart } from "../contexts/CartContext";
import logo from "../posters image/Capture_d_écran_16-12-2024_182813_looka.com-removebg-preview.png";

function Header() {
  const { isAuthenticated, user, logout, isAdmin } = useAuth();
  const { totalItems } = useCart();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  return (
    <header className="fixed top-0 w-full z-50 bg-black/90 backdrop-blur-sm border-b border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex-shrink-0">
            <img className="h-12 w-auto" src={logo} alt="I-POSTER" />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link 
              to="/" 
              className="text-white hover:text-red-400 transition-colors duration-200"
            >
              Accueil
            </Link>
            
            {/* Catalogue Dropdown */}
            <div className="relative group">
              <button className="text-white hover:text-red-400 transition-colors duration-200 flex items-center">
                Catalogue
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <Link 
                  to="/music-posters" 
                  className="block px-4 py-2 text-sm text-white hover:bg-red-600 hover:text-white transition-colors duration-200"
                >
                  Music Posters
                </Link>
                <Link 
                  to="/movies-posters" 
                  className="block px-4 py-2 text-sm text-white hover:bg-red-600 hover:text-white transition-colors duration-200"
                >
                  Movies & Series
                </Link>
              </div>
            </div>

            <Link 
              to="/about" 
              className="text-white hover:text-red-400 transition-colors duration-200"
            >
              About Us
            </Link>
          </nav>

          {/* Right side - Cart, Auth, Admin */}
          <div className="flex items-center space-x-4">
            {/* Cart */}
            <Link 
              to="/cart" 
              className="relative text-white hover:text-red-400 transition-colors duration-200"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6" />
              </svg>
              {totalItems > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {totalItems}
                </span>
              )}
            </Link>

            {/* Authentication */}
            {isAuthenticated ? (
              <div className="relative group">
                <button className="text-white hover:text-red-400 transition-colors duration-200 flex items-center">
                  <svg className="h-6 w-6 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  {user?.name}
                </button>
                <div className="absolute right-0 mt-2 w-48 bg-black/95 backdrop-blur-sm rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Link 
                    to="/profile" 
                    className="block px-4 py-2 text-sm text-white hover:bg-red-600 transition-colors duration-200"
                  >
                    Profile
                  </Link>
                  <Link 
                    to="/orders" 
                    className="block px-4 py-2 text-sm text-white hover:bg-red-600 transition-colors duration-200"
                  >
                    My Orders
                  </Link>
                  {isAdmin() && (
                    <Link 
                      to="/admin" 
                      className="block px-4 py-2 text-sm text-white hover:bg-red-600 transition-colors duration-200"
                    >
                      Admin Dashboard
                    </Link>
                  )}
                  <button 
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-red-600 transition-colors duration-200"
                  >
                    Logout
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link 
                  to="/login" 
                  className="text-white hover:text-red-400 transition-colors duration-200"
                >
                  Login
                </Link>
                <Link 
                  to="/signup" 
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors duration-200"
                >
                  Sign Up
                </Link>
              </div>
            )}

            {/* Mobile menu button */}
            <button 
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden text-white hover:text-red-400 transition-colors duration-200"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-black/95 backdrop-blur-sm rounded-md mt-2">
              <Link 
                to="/" 
                className="block px-3 py-2 text-white hover:bg-red-600 rounded-md transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Accueil
              </Link>
              <Link 
                to="/music-posters" 
                className="block px-3 py-2 text-white hover:bg-red-600 rounded-md transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Music Posters
              </Link>
              <Link 
                to="/movies-posters" 
                className="block px-3 py-2 text-white hover:bg-red-600 rounded-md transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Movies & Series
              </Link>
              <Link 
                to="/about" 
                className="block px-3 py-2 text-white hover:bg-red-600 rounded-md transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                About Us
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}

export default Header;
