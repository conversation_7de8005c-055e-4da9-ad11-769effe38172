<?php

/**
 * Simple API Server for I-POSTER
 * This serves as a basic API endpoint for testing
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:3001');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Session-ID');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

// Get request info
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/api', '', $path); // Remove /api prefix if present

// Route handling
switch ($path) {
    case '/posters':
        if ($method === 'GET') {
            handleGetPosters($pdo);
        }
        break;
        
    case '/test':
        if ($method === 'GET') {
            echo json_encode([
                'message' => 'API is working!',
                'timestamp' => date('Y-m-d H:i:s'),
                'method' => $method,
                'path' => $path
            ]);
        }
        break;
        
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found']);
        break;
}

function handleGetPosters($pdo) {
    try {
        // Get query parameters
        $category = $_GET['category'] ?? null;
        $featured = $_GET['featured'] ?? null;
        $per_page = (int)($_GET['per_page'] ?? 12);
        $page = (int)($_GET['page'] ?? 1);
        
        // Build query
        $sql = "SELECT * FROM posters WHERE status = 'active'";
        $params = [];
        
        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }
        
        if ($featured) {
            $sql .= " AND featured = 1";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        // Add pagination
        $offset = ($page - 1) * $per_page;
        $sql .= " LIMIT ? OFFSET ?";
        $params[] = $per_page;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $posters = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convert JSON fields
        foreach ($posters as &$poster) {
            if ($poster['tags']) {
                $poster['tags'] = json_decode($poster['tags'], true);
            }
            $poster['featured'] = (bool)$poster['featured'];
            $poster['price'] = (float)$poster['price'];
        }
        
        // Get total count for pagination
        $countSql = "SELECT COUNT(*) FROM posters WHERE status = 'active'";
        $countParams = [];
        
        if ($category) {
            $countSql .= " AND category = ?";
            $countParams[] = $category;
        }
        
        if ($featured) {
            $countSql .= " AND featured = 1";
        }
        
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($countParams);
        $total = $countStmt->fetchColumn();
        
        $response = [
            'data' => $posters,
            'current_page' => $page,
            'per_page' => $per_page,
            'total' => (int)$total,
            'last_page' => ceil($total / $per_page)
        ];
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch posters: ' . $e->getMessage()]);
    }
}
