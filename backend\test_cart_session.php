<?php

echo "Testing Cart API with Session\n";
echo "=============================\n\n";

// Start session to maintain session ID
session_start();
$sessionId = session_id();
echo "Session ID: $sessionId\n\n";

// Test add to cart
$data = json_encode(['poster_id' => 1, 'quantity' => 1]);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Cookie: PHPSESSID=' . $sessionId
        ],
        'content' => $data
    ]
]);

echo "1. Testing Add to Cart:\n";
$result = file_get_contents('http://localhost:8000/api/cart/add', false, $context);
echo "Response: " . $result . "\n\n";

// Test get cart with same session
$getContext = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Cookie: PHPSESSID=' . $sessionId
    ]
]);

echo "2. Testing Get Cart:\n";
$cartResult = file_get_contents('http://localhost:8000/api/cart', false, $getContext);
echo "Response: " . $cartResult . "\n\n";

// Check database directly
echo "3. Checking Database:\n";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=poster_shop', 'root', '');
    $stmt = $pdo->query("SELECT c.session_id, ci.*, p.title FROM carts c JOIN cart_items ci ON c.id = ci.cart_id JOIN posters p ON ci.poster_id = p.id ORDER BY ci.created_at DESC LIMIT 5");
    while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "- Session: {$row['session_id']}, Item: {$row['title']}, Qty: {$row['quantity']}, Price: {$row['price']}\n";
    }
} catch(Exception $e) {
    echo "DB Error: " . $e->getMessage() . "\n";
}

?>
