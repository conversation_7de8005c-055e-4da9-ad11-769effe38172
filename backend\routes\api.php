<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\PosterController;

// Public routes
Route::get('/posters', [PosterController::class, 'index']);
Route::get('/posters/{id}', [PosterController::class, 'show']);

// Protected routes (add auth middleware in real implementation)
Route::post('/posters', [PosterController::class, 'store']);
Route::put('/posters/{id}', [PosterController::class, 'update']);
Route::delete('/posters/{id}', [PosterController::class, 'destroy']);