<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\PosterController;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CartController;
use App\Http\Controllers\API\OrderController;
use App\Http\Controllers\API\AdminController;
use App\Http\Controllers\API\ImageUploadController;

// Public routes
Route::get('/posters', [PosterController::class, 'index']);
Route::get('/posters/{id}', [PosterController::class, 'show']);

// Authentication routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Cart routes (works for both guests and authenticated users)
Route::get('/cart', [CartController::class, 'index']);
Route::post('/cart/add', [CartController::class, 'addItem']);
Route::put('/cart/items/{posterId}', [CartController::class, 'updateItem']);
Route::delete('/cart/items/{posterId}', [CartController::class, 'removeItem']);
Route::delete('/cart/clear', [CartController::class, 'clear']);

// Order routes (public for placing orders)
Route::post('/orders', [OrderController::class, 'store']);

// Protected routes (require authentication)
Route::middleware('auth:sanctum')->group(function () {
    // User routes
    Route::get('/user', [AuthController::class, 'user']);
    Route::put('/user/profile', [AuthController::class, 'updateProfile']);
    Route::put('/user/password', [AuthController::class, 'changePassword']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // User orders
    Route::get('/orders', [OrderController::class, 'index']);
    Route::get('/orders/{id}', [OrderController::class, 'show']);
});

// Admin routes (require admin role)
Route::middleware(['auth:sanctum', 'admin'])->group(function () {
    // Poster management
    Route::post('/posters', [PosterController::class, 'store']);
    Route::put('/posters/{id}', [PosterController::class, 'update']);
    Route::delete('/posters/{id}', [PosterController::class, 'destroy']);

    // Order management
    Route::put('/orders/{id}/status', [OrderController::class, 'updateStatus']);

    // Admin dashboard
    Route::get('/admin/dashboard', [AdminController::class, 'dashboard']);
    Route::get('/admin/users', [AdminController::class, 'users']);
    Route::put('/admin/users/{userId}/role', [AdminController::class, 'updateUserRole']);

    // Image upload
    Route::post('/upload/image', [ImageUploadController::class, 'upload']);
});