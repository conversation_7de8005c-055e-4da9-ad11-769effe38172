<?php

echo "Testing Cart API\n";
echo "================\n\n";

// Test add to cart
$data = json_encode(['poster_id' => 1, 'quantity' => 1]);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $data
    ]
]);

echo "1. Testing Add to Cart:\n";
$result = file_get_contents('http://localhost:8000/api/cart/add', false, $context);
echo "Response: " . $result . "\n\n";

// Test get cart
echo "2. Testing Get Cart:\n";
$cartResult = file_get_contents('http://localhost:8000/api/cart');
echo "Response: " . $cartResult . "\n\n";

?>
