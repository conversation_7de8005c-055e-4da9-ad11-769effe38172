<?php

// Test database connection
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n";
    
    // Test if posters table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'posters'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Posters table exists!\n";
        
        // Count posters
        $countStmt = $pdo->query("SELECT COUNT(*) FROM posters");
        $count = $countStmt->fetchColumn();
        echo "✅ Found $count posters in database!\n";
        
        // Test music posters
        $musicStmt = $pdo->prepare("SELECT COUNT(*) FROM posters WHERE category = 'music'");
        $musicStmt->execute();
        $musicCount = $musicStmt->fetchColumn();
        echo "✅ Found $musicCount music posters!\n";
        
        // Test movie posters
        $movieStmt = $pdo->prepare("SELECT COUNT(*) FROM posters WHERE category = 'movie'");
        $movieStmt->execute();
        $movieCount = $movieStmt->fetchColumn();
        echo "✅ Found $movieCount movie posters!\n";
        
    } else {
        echo "❌ Posters table does not exist!\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

?>
