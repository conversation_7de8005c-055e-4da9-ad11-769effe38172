<?php

/**
 * Verify Image Paths and Accessibility
 * Checks if all poster images are accessible
 */

echo "🖼️  Verifying I-POSTER Image Paths\n";
echo "==================================\n\n";

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database successfully!\n\n";
    
    // Get all posters with their image paths
    $stmt = $pdo->query("SELECT id, title, category, image_path, featured FROM posters WHERE status = 'active' ORDER BY category, id");
    $posters = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📊 Found " . count($posters) . " active posters\n\n";
    
    $categories = [];
    $featuredCount = 0;
    $missingImages = [];
    
    foreach ($posters as $poster) {
        $category = $poster['category'];
        if (!isset($categories[$category])) {
            $categories[$category] = 0;
        }
        $categories[$category]++;
        
        if ($poster['featured']) {
            $featuredCount++;
        }
        
        // Check if image file exists
        $imagePath = '../public' . $poster['image_path'];
        if (!file_exists($imagePath)) {
            $missingImages[] = [
                'id' => $poster['id'],
                'title' => $poster['title'],
                'path' => $poster['image_path'],
                'full_path' => $imagePath
            ];
        }
        
        $featuredIcon = $poster['featured'] ? '⭐' : '  ';
        $existsIcon = file_exists($imagePath) ? '✅' : '❌';
        
        echo sprintf(
            "%s %s [%s] %s - %s\n",
            $featuredIcon,
            $existsIcon,
            strtoupper($category),
            $poster['title'],
            $poster['image_path']
        );
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "📈 SUMMARY\n";
    echo str_repeat("=", 50) . "\n\n";
    
    echo "📂 Categories:\n";
    foreach ($categories as $category => $count) {
        $icon = match($category) {
            'music' => '🎵',
            'album' => '💿',
            'movie' => '🎬',
            default => '📁'
        };
        echo "   $icon " . ucfirst($category) . ": $count posters\n";
    }
    
    echo "\n⭐ Featured Posters: $featuredCount\n";
    echo "🖼️  Total Images: " . count($posters) . "\n";
    echo "❌ Missing Images: " . count($missingImages) . "\n\n";
    
    if (!empty($missingImages)) {
        echo "🚨 MISSING IMAGES:\n";
        echo str_repeat("-", 30) . "\n";
        foreach ($missingImages as $missing) {
            echo "ID {$missing['id']}: {$missing['title']}\n";
            echo "   Expected: {$missing['path']}\n";
            echo "   Full path: {$missing['full_path']}\n\n";
        }
    } else {
        echo "🎉 All images are accessible!\n\n";
    }
    
    // Test API endpoints
    echo "🔗 Testing API Endpoints:\n";
    echo str_repeat("-", 25) . "\n";
    
    $endpoints = [
        'All Posters' => 'http://localhost:8000/api/posters',
        'Featured Posters' => 'http://localhost:8000/api/posters?featured=1',
        'Music Posters' => 'http://localhost:8000/api/posters?category=music',
        'Album Posters' => 'http://localhost:8000/api/posters?category=album',
        'Movie Posters' => 'http://localhost:8000/api/posters?category=movie'
    ];
    
    foreach ($endpoints as $name => $url) {
        $response = @file_get_contents($url);
        if ($response) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                $count = $data['total'] ?? count($data['data'] ?? []);
                echo "✅ $name: $count items\n";
            } else {
                echo "❌ $name: Invalid response\n";
            }
        } else {
            echo "❌ $name: Connection failed\n";
        }
    }
    
    echo "\n🎨 I-POSTER System Status: ";
    if (empty($missingImages)) {
        echo "🟢 READY\n";
    } else {
        echo "🟡 NEEDS ATTENTION\n";
    }
    
    echo "\n📋 Next Steps:\n";
    echo "• Visit http://localhost:3000 to test the frontend\n";
    echo "• Check navigation: Featured → Album → Music → Movies\n";
    echo "• Verify images display correctly\n";
    echo "• Test cart and checkout functionality\n\n";
    
} catch (PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

?>
