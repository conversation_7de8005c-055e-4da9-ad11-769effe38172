import React, { useState, useEffect } from 'react';
import { postersAPI, adminAPI } from '../../services/api';

function AdminPosters() {
  const [posters, setPosters] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    image_path: '',
    category: 'music',
    artist: '',
    size: 'A3',
    frame_type: 'black',
    featured: false,
    quantity: 10,
    tags: ''
  });
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState('');
  const [imageFile, setImageFile] = useState(null);

  useEffect(() => {
    fetchPosters();
  }, []);

  const fetchPosters = async () => {
    try {
      setLoading(true);
      const response = await postersAPI.getAll();
      if (response.data.success) {
        setPosters(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching posters:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setImageFile(file);
    if (file) {
      setFormData(prev => ({ ...prev, image_path: `/images/posters/${file.name}` }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.price || formData.price <= 0) newErrors.price = 'Valid price is required';
    if (!formData.artist.trim()) newErrors.artist = 'Artist name is required';
    if (!formData.image_path.trim() && !imageFile) newErrors.image_path = 'Image is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      setSuccess('');
      return;
    }

    try {
      setLoading(true);
      setErrors({});

      let imagePath = formData.image_path;

      // Upload image if file is selected
      if (imageFile) {
        try {
          const uploadResponse = await adminAPI.uploadImage(imageFile);
          imagePath = uploadResponse.data.image_path;
        } catch (uploadError) {
          setErrors({ image_path: 'Failed to upload image' });
          return;
        }
      }

      // Prepare poster data
      const posterData = {
        ...formData,
        image_path: imagePath,
        price: parseFloat(formData.price),
        quantity: parseInt(formData.quantity),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      };

      const response = await postersAPI.create(posterData);

      if (response.data.success) {
        setSuccess('Poster added successfully!');
        setFormData({
          title: '',
          description: '',
          price: '',
          image_path: '',
          category: 'music',
          artist: '',
          size: 'A3',
          frame_type: 'black',
          featured: false,
          quantity: 10,
          tags: ''
        });
        setImageFile(null);
        setShowAddForm(false);
        fetchPosters(); // Refresh the list

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(''), 3000);
      }
    } catch (error) {
      setErrors({
        submit: error.response?.data?.message || 'Failed to add poster'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-primary-dark to-secondary-dark py-16">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-pure-white mb-4">
            🎨 Manage Posters
          </h1>
          <p className="text-neutral-medium">Add, edit, and manage your poster collection</p>
        </div>

        {/* Success Message */}
        {success && (
          <div className="mb-6 bg-accent-red/20 border border-accent-red text-accent-red-light px-4 py-3 rounded-lg">
            ✅ {success}
          </div>
        )}

        {/* Add New Poster Button */}
        <div className="mb-8 text-center">
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-accent-red hover:bg-accent-red-dark text-pure-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            {showAddForm ? '❌ Cancel' : '➕ Add New Poster'}
          </button>
        </div>

        {/* Add Poster Form */}
        {showAddForm && (
          <div className="bg-secondary-dark rounded-xl p-8 mb-8 border border-neutral-medium/20">
            <h2 className="text-2xl font-bold text-pure-white mb-6">Add New Poster</h2>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Message */}
              {errors.submit && (
                <div className="bg-accent-red-dark/20 border border-accent-red text-accent-red-light px-4 py-3 rounded-lg">
                  {errors.submit}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-neutral-light mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white placeholder-neutral-medium focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                    placeholder="Enter poster title"
                  />
                  {errors.title && <p className="text-accent-red-light text-sm mt-1">{errors.title}</p>}
                </div>

                {/* Artist */}
                <div>
                  <label className="block text-sm font-medium text-neutral-light mb-2">
                    Artist *
                  </label>
                  <input
                    type="text"
                    name="artist"
                    value={formData.artist}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white placeholder-neutral-medium focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                    placeholder="Enter artist name"
                  />
                  {errors.artist && <p className="text-accent-red-light text-sm mt-1">{errors.artist}</p>}
                </div>

                {/* Price */}
                <div>
                  <label className="block text-sm font-medium text-neutral-light mb-2">
                    Price (MAD) *
                  </label>
                  <input
                    type="number"
                    name="price"
                    value={formData.price}
                    onChange={handleInputChange}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white placeholder-neutral-medium focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                    placeholder="0.00"
                  />
                  {errors.price && <p className="text-accent-red-light text-sm mt-1">{errors.price}</p>}
                </div>

                {/* Quantity */}
                <div>
                  <label className="block text-sm font-medium text-neutral-light mb-2">
                    Quantity
                  </label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleInputChange}
                    min="0"
                    className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white placeholder-neutral-medium focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                  />
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-neutral-light mb-2">
                    Category
                  </label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                  >
                    <option value="music">Music</option>
                    <option value="album">Album</option>
                    <option value="movie">Movie</option>
                  </select>
                </div>

                {/* Size */}
                <div>
                  <label className="block text-sm font-medium text-neutral-light mb-2">
                    Size
                  </label>
                  <select
                    name="size"
                    value={formData.size}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                  >
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="A2">A2</option>
                    <option value="A1">A1</option>
                  </select>
                </div>

                {/* Frame Type */}
                <div>
                  <label className="block text-sm font-medium text-neutral-light mb-2">
                    Frame Type
                  </label>
                  <select
                    name="frame_type"
                    value={formData.frame_type}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                  >
                    <option value="black">Black</option>
                    <option value="white">White</option>
                    <option value="wood">Wood</option>
                    <option value="none">No Frame</option>
                  </select>
                </div>

                {/* Featured */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="featured"
                    checked={formData.featured}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-accent-red focus:ring-accent-red border-neutral-medium/30 bg-primary-dark rounded"
                  />
                  <label className="ml-2 block text-sm text-neutral-light">
                    Featured Poster
                  </label>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-neutral-light mb-2">
                  Description *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows="4"
                  className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white placeholder-neutral-medium focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                  placeholder="Enter poster description"
                />
                {errors.description && <p className="text-accent-red-light text-sm mt-1">{errors.description}</p>}
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-neutral-light mb-2">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  name="tags"
                  value={formData.tags}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white placeholder-neutral-medium focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                  placeholder="e.g., rock, classic, vintage"
                />
              </div>

              {/* Image Upload */}
              <div>
                <label className="block text-sm font-medium text-neutral-light mb-2">
                  Image *
                </label>
                <div className="space-y-4">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-accent-red file:text-pure-white hover:file:bg-accent-red-dark transition-all duration-200"
                  />
                  <div className="text-sm text-neutral-medium">
                    Or enter image URL:
                  </div>
                  <input
                    type="url"
                    name="image_path"
                    value={formData.image_path}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-primary-dark border border-neutral-medium/30 rounded-lg text-pure-white placeholder-neutral-medium focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-all duration-200"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
                {errors.image_path && <p className="text-accent-red-light text-sm mt-1">{errors.image_path}</p>}
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  className="px-6 py-3 bg-neutral-medium hover:bg-neutral-light text-primary-dark rounded-lg font-semibold transition-all duration-300"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-3 bg-accent-red hover:bg-accent-red-dark text-pure-white rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Adding...
                    </div>
                  ) : (
                    'Add Poster'
                  )}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Posters List */}
        <div className="bg-secondary-dark rounded-xl p-8 border border-neutral-medium/20">
          <h2 className="text-2xl font-bold text-pure-white mb-6">Current Posters</h2>

          {loading && !showAddForm ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-red mx-auto"></div>
              <p className="text-neutral-medium mt-4">Loading posters...</p>
            </div>
          ) : posters.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🎨</div>
              <h3 className="text-xl font-semibold text-pure-white mb-2">No posters yet</h3>
              <p className="text-neutral-medium">
                Add your first poster to get started!
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {posters.map((poster) => (
                <div key={poster.id} className="bg-primary-dark rounded-lg overflow-hidden border border-neutral-medium/10 hover:border-accent-red/30 transition-all duration-300">
                  <div className="relative">
                    <img
                      src={poster.image_path}
                      alt={poster.title}
                      className="w-full h-48 object-cover"
                      onError={(e) => {
                        e.target.src = '/placeholder-poster.jpg';
                      }}
                    />
                    {poster.featured && (
                      <div className="absolute top-2 left-2 bg-accent-red text-pure-white px-2 py-1 rounded-full text-xs font-bold">
                        ⭐ Featured
                      </div>
                    )}
                    <div className="absolute top-2 right-2 bg-secondary-dark/80 text-pure-white px-2 py-1 rounded-full text-xs">
                      {poster.category}
                    </div>
                  </div>

                  <div className="p-4">
                    <h3 className="text-pure-white font-semibold text-lg truncate mb-1">
                      {poster.title}
                    </h3>
                    <p className="text-neutral-medium text-sm mb-2">
                      by {poster.artist}
                    </p>
                    <p className="text-neutral-light text-sm mb-3 line-clamp-2">
                      {poster.description}
                    </p>

                    <div className="flex justify-between items-center mb-3">
                      <span className="text-pure-white font-bold">
                        {new Intl.NumberFormat('fr-MA', {
                          style: 'currency',
                          currency: 'MAD'
                        }).format(poster.price)}
                      </span>
                      <span className="text-neutral-medium text-sm">
                        Stock: {poster.quantity}
                      </span>
                    </div>

                    <div className="flex justify-between items-center text-xs text-neutral-medium">
                      <span>Size: {poster.size}</span>
                      <span>Frame: {poster.frame_type}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default AdminPosters;
