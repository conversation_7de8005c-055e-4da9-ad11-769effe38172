<?php

/**
 * Admin API for I-POSTER
 * Handles admin operations: order management, poster management, dashboard
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Session-ID');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/backend/', '', $path);
$path = trim($path, '/');

// Get request body for POST/PUT requests
$input = json_decode(file_get_contents('php://input'), true);

// Simple admin authentication (in production, use proper JWT tokens)
$adminToken = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
if (!isValidAdminToken($pdo, $adminToken)) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit();
}

// Route handling
switch ($path) {
    case 'admin/dashboard':
        handleDashboard($pdo, $method);
        break;
    
    case 'admin/orders':
        handleAdminOrders($pdo, $method, $input);
        break;
    
    case 'admin/posters':
        handleAdminPosters($pdo, $method, $input);
        break;
    
    default:
        if (preg_match('/^admin\/orders\/(\d+)\/status$/', $path, $matches)) {
            updateOrderStatus($pdo, $method, $input, $matches[1]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
        }
        break;
}

/**
 * Check if admin token is valid
 */
function isValidAdminToken($pdo, $token) {
    if (empty($token)) return false;
    
    // Remove "Bearer " prefix if present
    $token = str_replace('Bearer ', '', $token);
    
    $stmt = $pdo->prepare("SELECT role FROM users WHERE remember_token = ?");
    $stmt->execute([$token]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return $user && $user['role'] === 'admin';
}

/**
 * Handle admin dashboard
 */
function handleDashboard($pdo, $method) {
    if ($method !== 'GET') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        // Get dashboard statistics
        $stats = [];

        // Total orders
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
        $stats['total_orders'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Orders by status
        $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM orders GROUP BY status");
        $statusCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stats['orders_by_status'] = [];
        foreach ($statusCounts as $status) {
            $stats['orders_by_status'][$status['status']] = $status['count'];
        }

        // Total revenue
        $stmt = $pdo->query("SELECT SUM(total_amount) as revenue FROM orders WHERE status != 'cancelled'");
        $stats['total_revenue'] = (float)($stmt->fetch(PDO::FETCH_ASSOC)['revenue'] ?? 0);

        // Total posters
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM posters WHERE status = 'active'");
        $stats['total_posters'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Recent orders
        $stmt = $pdo->query("
            SELECT id, order_number, customer_name, total_amount, status, created_at 
            FROM orders 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stats['recent_orders'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Low stock posters
        $stmt = $pdo->query("
            SELECT id, title, quantity 
            FROM posters 
            WHERE status = 'active' AND quantity <= 5 
            ORDER BY quantity ASC
        ");
        $stats['low_stock_posters'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to fetch dashboard data: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle admin orders
 */
function handleAdminOrders($pdo, $method, $input) {
    if ($method !== 'GET') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        $status = $_GET['status'] ?? null;
        $limit = $_GET['limit'] ?? 50;
        $offset = $_GET['offset'] ?? 0;

        $sql = "SELECT * FROM orders";
        $params = [];

        if ($status) {
            $sql .= " WHERE status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = (int)$limit;
        $params[] = (int)$offset;

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get order items for each order
        foreach ($orders as &$order) {
            $stmt = $pdo->prepare("SELECT * FROM order_items WHERE order_id = ?");
            $stmt->execute([$order['id']]);
            $order['items'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $order['total_amount'] = (float)$order['total_amount'];
        }

        echo json_encode([
            'success' => true,
            'data' => $orders
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to fetch orders: ' . $e->getMessage()
        ]);
    }
}

/**
 * Update order status
 */
function updateOrderStatus($pdo, $method, $input, $orderId) {
    if ($method !== 'PUT') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        if (!isset($input['status'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Status is required']);
            return;
        }

        $validStatuses = ['pending', 'confirmed', 'shipped', 'delivered', 'cancelled'];
        if (!in_array($input['status'], $validStatuses)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid status']);
            return;
        }

        $stmt = $pdo->prepare("UPDATE orders SET status = ?, admin_notes = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([
            $input['status'],
            $input['admin_notes'] ?? null,
            $orderId
        ]);

        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Order not found']);
            return;
        }

        echo json_encode([
            'success' => true,
            'message' => 'Order status updated successfully'
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to update order status: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle admin posters
 */
function handleAdminPosters($pdo, $method, $input) {
    switch ($method) {
        case 'GET':
            getAdminPosters($pdo);
            break;
        case 'POST':
            createPoster($pdo, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
}

/**
 * Get all posters for admin
 */
function getAdminPosters($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM posters ORDER BY created_at DESC");
        $posters = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($posters as &$poster) {
            if (isset($poster['tags']) && $poster['tags']) {
                $poster['tags'] = json_decode($poster['tags'], true);
            }
            $poster['featured'] = (bool)$poster['featured'];
            $poster['price'] = (float)$poster['price'];
            $poster['quantity'] = (int)$poster['quantity'];
        }

        echo json_encode([
            'success' => true,
            'data' => $posters
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to fetch posters: ' . $e->getMessage()
        ]);
    }
}

?>
