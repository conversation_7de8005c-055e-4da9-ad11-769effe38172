<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'poster_id',
        'quantity',
        'price',
        'poster_title',
        'poster_description'
    ];

    protected $casts = [
        'price' => 'decimal:2'
    ];

    // Relationships
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function poster()
    {
        return $this->belongsTo(Poster::class);
    }

    // Helper methods
    public function getSubtotal()
    {
        return $this->quantity * $this->price;
    }
}
