<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\Poster;
use Illuminate\Http\Request;

class CartController extends Controller
{
    public function index(Request $request)
    {
        $cart = $this->getOrCreateCart($request);
        
        return response()->json([
            'cart' => $cart->load(['items.poster']),
            'total_price' => $cart->getTotalPrice(),
            'total_items' => $cart->getTotalItems()
        ]);
    }

    public function addItem(Request $request)
    {
        $validated = $request->validate([
            'poster_id' => 'required|exists:posters,id',
            'quantity' => 'integer|min:1|max:10'
        ]);

        $poster = Poster::findOrFail($validated['poster_id']);
        
        // Check if poster is available
        if ($poster->quantity < ($validated['quantity'] ?? 1)) {
            return response()->json([
                'message' => 'Not enough stock available'
            ], 400);
        }

        $cart = $this->getOrCreateCart($request);
        $quantity = $validated['quantity'] ?? 1;
        
        $cartItem = $cart->addItem($poster->id, $quantity);

        return response()->json([
            'message' => 'Item added to cart',
            'cart_item' => $cartItem->load('poster'),
            'cart_total' => $cart->getTotalPrice(),
            'cart_items_count' => $cart->getTotalItems()
        ]);
    }

    public function updateItem(Request $request, $posterId)
    {
        $validated = $request->validate([
            'quantity' => 'required|integer|min:0|max:10'
        ]);

        $cart = $this->getOrCreateCart($request);
        $cartItem = $cart->updateItemQuantity($posterId, $validated['quantity']);

        if (!$cartItem && $validated['quantity'] > 0) {
            return response()->json([
                'message' => 'Item not found in cart'
            ], 404);
        }

        return response()->json([
            'message' => $validated['quantity'] > 0 ? 'Item updated' : 'Item removed from cart',
            'cart_total' => $cart->getTotalPrice(),
            'cart_items_count' => $cart->getTotalItems()
        ]);
    }

    public function removeItem(Request $request, $posterId)
    {
        $cart = $this->getOrCreateCart($request);
        $removed = $cart->removeItem($posterId);

        if (!$removed) {
            return response()->json([
                'message' => 'Item not found in cart'
            ], 404);
        }

        return response()->json([
            'message' => 'Item removed from cart',
            'cart_total' => $cart->getTotalPrice(),
            'cart_items_count' => $cart->getTotalItems()
        ]);
    }

    public function clear(Request $request)
    {
        $cart = $this->getOrCreateCart($request);
        $cart->clear();

        return response()->json([
            'message' => 'Cart cleared'
        ]);
    }

    private function getOrCreateCart(Request $request)
    {
        if ($request->user()) {
            // For authenticated users
            return Cart::firstOrCreate(
                ['user_id' => $request->user()->id],
                ['session_id' => null]
            );
        } else {
            // For guest users
            $sessionId = $request->header('X-Session-ID') ?? session()->getId();
            return Cart::firstOrCreate(
                ['session_id' => $sessionId],
                ['user_id' => null]
            );
        }
    }
}
