# I-POSTER E-commerce API Documentation

## Overview
Complete backend API for the I-POSTER e-commerce platform. Supports poster browsing, shopping cart, cash-on-delivery orders, user authentication, and admin management.

## Base URL
```
http://localhost:8000
```

## Authentication
- **Guest Users**: Use `X-Session-ID` header for cart management
- **Registered Users**: Use `Authorization: Bearer {token}` header
- **Admin Users**: Require admin role and valid token

## API Endpoints

### 🎨 Posters API

#### Get All Posters
```http
GET /api/posters
```

**Query Parameters:**
- `category` (optional): Filter by category (music, movie, art, sports, vintage)
- `featured` (optional): Get only featured posters (1 for featured)
- `limit` (optional): Limit number of results

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Blonde",
      "description": "<PERSON>'s masterpiece album poster",
      "price": 120.00,
      "quantity": 10,
      "image_path": "/images/blonde.jpg",
      "category": "music",
      "artist": "Frank Ocean",
      "size": "A4",
      "featured": true,
      "status": "active"
    }
  ],
  "total": 6
}
```

### 🛒 Cart API

#### Get Cart Contents
```http
GET /api/cart
Headers: X-Session-ID: {session_id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "cart_id": 1,
    "items": [
      {
        "id": 1,
        "poster_id": 1,
        "quantity": 2,
        "title": "Blonde",
        "price": 120.00,
        "subtotal": 240.00
      }
    ],
    "total": 240.00,
    "count": 2,
    "currency": "MAD"
  }
}
```

#### Add Item to Cart
```http
POST /api/cart/add
Headers: X-Session-ID: {session_id}
Content-Type: application/json

{
  "poster_id": 1,
  "quantity": 2
}
```

**Response:**
```json
{
  "success": true,
  "message": "Item added to cart successfully",
  "poster": {
    "id": 1,
    "title": "Blonde",
    "price": 120.00
  }
}
```

#### Update Cart Item
```http
PUT /api/cart/items/{poster_id}
Headers: X-Session-ID: {session_id}
Content-Type: application/json

{
  "quantity": 3
}
```

#### Remove Cart Item
```http
DELETE /api/cart/items/{poster_id}
Headers: X-Session-ID: {session_id}
```

#### Clear Cart
```http
DELETE /api/cart
Headers: X-Session-ID: {session_id}
```

### 📦 Orders API

#### Create Order (Cash on Delivery)
```http
POST /api/orders
Headers: X-Session-ID: {session_id}
Content-Type: application/json

{
  "customer_name": "John Doe",
  "customer_email": "<EMAIL>",
  "customer_phone": "+212600123456",
  "delivery_address": "123 Main St, Casablanca, Morocco",
  "notes": "Please deliver in the evening"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Order placed successfully",
  "data": {
    "order_id": 1,
    "order_number": "IP-20250702-ABC123",
    "total_amount": 240.00,
    "status": "pending",
    "payment_type": "Cash on Delivery"
  }
}
```

#### Track Order
```http
GET /orders/track?order_number=IP-20250702-ABC123&phone=+212600123456
```

#### Get Customer Orders
```http
GET /orders/customer?phone=+212600123456
```

### 👤 Authentication API

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+212600123456",
  "address": "123 Main St, Casablanca"
}
```

#### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "customer"
    },
    "token": "abc123def456..."
  }
}
```

### 🔧 Admin API

#### Get Dashboard Stats
```http
GET /admin/dashboard
Headers: Authorization: Bearer {admin_token}
```

#### Get All Orders (Admin)
```http
GET /admin/orders
Headers: Authorization: Bearer {admin_token}
```

#### Update Order Status
```http
PUT /admin/orders/{order_id}/status
Headers: Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "status": "confirmed",
  "admin_notes": "Order confirmed and being prepared"
}
```

## Order Status Flow

1. **pending** - Order received and being processed
2. **confirmed** - Order confirmed and being prepared
3. **shipped** - Order shipped and on its way
4. **delivered** - Order successfully delivered
5. **cancelled** - Order cancelled

## Error Responses

All error responses follow this format:
```json
{
  "success": false,
  "error": "Error message description"
}
```

**Common HTTP Status Codes:**
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `405` - Method Not Allowed
- `500` - Internal Server Error

## Data Validation

### Phone Numbers
Accepts Moroccan phone number formats:
- `+212XXXXXXXXX`
- `0XXXXXXXXX`
- `XXXXXXXXX`

### Email
Standard email validation using PHP's `FILTER_VALIDATE_EMAIL`

### Passwords
- Minimum 8 characters
- Must contain at least one letter
- Must contain at least one number

## Database Schema

### Tables
- `users` - User accounts and authentication
- `posters` - Poster catalog with details
- `carts` - Shopping cart sessions
- `cart_items` - Items in shopping carts
- `orders` - Customer orders
- `order_items` - Items in orders
- `personal_access_tokens` - API authentication tokens

## Security Features

- Input sanitization and validation
- SQL injection prevention using prepared statements
- XSS protection with HTML entity encoding
- CORS headers for cross-origin requests
- Session-based cart management for guests
- Token-based authentication for users
- Role-based access control for admin functions

## Testing

Run the API test suite:
```bash
cd backend
php test_api.php
```

## Default Admin Account
- **Email**: <EMAIL>
- **Password**: password123
- **Role**: admin

## Default Customer Account
- **Email**: <EMAIL>
- **Password**: password123
- **Role**: customer
