import React from 'react';
import { Link } from 'react-router-dom';

function AdminDashboard() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-16">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            🛠️ Admin Dashboard
          </h1>
          <p className="text-gray-400">Manage your I-POSTER store</p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Posters Management */}
          <Link to="/admin/posters" className="bg-gray-800 hover:bg-gray-700 rounded-lg p-6 transition-colors duration-200">
            <div className="text-center">
              <div className="text-4xl mb-4">🎨</div>
              <h3 className="text-xl font-semibold text-white mb-2">Manage Posters</h3>
              <p className="text-gray-400">Add, edit, and remove posters from your collection</p>
            </div>
          </Link>

          {/* Orders Management */}
          <Link to="/admin/orders" className="bg-gray-800 hover:bg-gray-700 rounded-lg p-6 transition-colors duration-200">
            <div className="text-center">
              <div className="text-4xl mb-4">📦</div>
              <h3 className="text-xl font-semibold text-white mb-2">Manage Orders</h3>
              <p className="text-gray-400">View and update order status</p>
            </div>
          </Link>

          {/* Users Management */}
          <Link to="/admin/users" className="bg-gray-800 hover:bg-gray-700 rounded-lg p-6 transition-colors duration-200">
            <div className="text-center">
              <div className="text-4xl mb-4">👥</div>
              <h3 className="text-xl font-semibold text-white mb-2">Manage Users</h3>
              <p className="text-gray-400">View and manage user accounts</p>
            </div>
          </Link>
        </div>

        {/* Quick Stats */}
        <div className="mt-12 grid md:grid-cols-4 gap-6">
          <div className="bg-red-900/20 rounded-lg p-6 text-center">
            <div className="text-2xl font-bold text-red-400 mb-2">14</div>
            <div className="text-gray-400">Total Posters</div>
          </div>
          <div className="bg-blue-900/20 rounded-lg p-6 text-center">
            <div className="text-2xl font-bold text-blue-400 mb-2">0</div>
            <div className="text-gray-400">Pending Orders</div>
          </div>
          <div className="bg-green-900/20 rounded-lg p-6 text-center">
            <div className="text-2xl font-bold text-green-400 mb-2">2</div>
            <div className="text-gray-400">Total Users</div>
          </div>
          <div className="bg-yellow-900/20 rounded-lg p-6 text-center">
            <div className="text-2xl font-bold text-yellow-400 mb-2">0 MAD</div>
            <div className="text-gray-400">Total Revenue</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AdminDashboard;
