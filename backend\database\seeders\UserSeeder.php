<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+212600000000',
            'address' => 'Admin Address, Casablanca, Morocco',
            'role' => 'admin'
        ]);

        // Create test customer
        User::create([
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+212611111111',
            'address' => '123 Test Street, Rabat, Morocco',
            'role' => 'customer'
        ]);
    }
}
