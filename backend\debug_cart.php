<?php

echo "🛒 Debugging Cart Issue\n";
echo "=======================\n\n";

$sessionId = 'debug_session_' . time();

// Step 1: Add item to cart
echo "1. Adding item to cart...\n";
$cartData = json_encode(['poster_id' => 1, 'quantity' => 1]);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'X-Session-ID: ' . $sessionId
        ],
        'content' => $cartData
    ]
]);

$addResult = file_get_contents('http://localhost:8000/api/cart/add', false, $context);
echo "Add Response: " . $addResult . "\n\n";

// Step 2: Get cart immediately after adding
echo "2. Getting cart contents...\n";
$getContext = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'X-Session-ID: ' . $sessionId
    ]
]);

$cartResult = file_get_contents('http://localhost:8000/api/cart', false, $getContext);
echo "Cart Response: " . $cartResult . "\n\n";

// Step 3: Check database directly
echo "3. Checking database for this session...\n";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=poster_shop', 'root', '');
    
    // Find cart for this session
    $stmt = $pdo->prepare("SELECT * FROM carts WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($cart) {
        echo "Cart found: ID = {$cart['id']}, Session = {$cart['session_id']}\n";
        
        // Check cart items
        $stmt = $pdo->prepare("
            SELECT ci.*, p.title, p.price as poster_price 
            FROM cart_items ci 
            JOIN posters p ON ci.poster_id = p.id 
            WHERE ci.cart_id = ?
        ");
        $stmt->execute([$cart['id']]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Cart items found: " . count($items) . "\n";
        foreach ($items as $item) {
            echo "- {$item['title']}: Qty={$item['quantity']}, Price={$item['price']}, PosterPrice={$item['poster_price']}\n";
        }
    } else {
        echo "No cart found for session: $sessionId\n";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

echo "\n";

// Step 4: Test with different session (simulate frontend)
echo "4. Testing with frontend-style session...\n";
$frontendSession = 'guest_abc123_' . time();

$cartData2 = json_encode(['poster_id' => 2, 'quantity' => 2]);
$context2 = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'X-Session-ID: ' . $frontendSession
        ],
        'content' => $cartData2
    ]
]);

$addResult2 = file_get_contents('http://localhost:8000/api/cart/add', false, $context2);
echo "Frontend-style Add Response: " . $addResult2 . "\n";

$getContext2 = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'X-Session-ID: ' . $frontendSession
    ]
]);

$cartResult2 = file_get_contents('http://localhost:8000/api/cart', false, $getContext2);
echo "Frontend-style Cart Response: " . $cartResult2 . "\n\n";

echo "🔍 Analysis:\n";
echo "- Check if session IDs are being passed correctly\n";
echo "- Verify cart items are being stored with correct cart_id\n";
echo "- Ensure getCart function is querying the right cart\n";

?>
