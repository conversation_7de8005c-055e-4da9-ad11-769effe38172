<?php
require_once 'setup_database.php';

try {
    // Create test users for I-POSTER platform
    echo "Creating user accounts for I-POSTER platform...\n";

    // Clear existing users first
    $pdo->exec("DELETE FROM users");
    echo "🗑️ Cleared existing users\n";

    // Admin user
    $stmt = $pdo->prepare("INSERT INTO users (id, name, email, password, role, phone, address, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");

    // Admin Account
    $stmt->execute([
        1,
        'Admin I-POSTER',
        '<EMAIL>',
        password_hash('admin123', PASSWORD_DEFAULT),
        'admin',
        '+212 6 12 34 56 78',
        'Casablanca, Morocco',
        date('Y-m-d H:i:s')
    ]);
    echo "✅ Admin account created: <EMAIL> / admin123\n";

    // Customer 1 - Music Lover
    $stmt->execute([
        2,
        '<PERSON>',
        '<EMAIL>',
        password_hash('password123', PASSWORD_DEFAULT),
        'customer',
        '+212 6 11 22 33 44',
        '123 Rue Mohammed V, Rabat, Morocco',
        date('Y-m-d H:i:s')
    ]);
    echo "✅ Customer account created: <EMAIL> / password123\n";

    // Customer 2 - Movie Enthusiast
    $stmt->execute([
        3,
        'Fatima Zahra',
        '<EMAIL>',
        password_hash('password123', PASSWORD_DEFAULT),
        'customer',
        '+212 6 55 66 77 88',
        '456 Avenue Hassan II, Marrakech, Morocco',
        date('Y-m-d H:i:s')
    ]);
    echo "✅ Customer account created: <EMAIL> / password123\n";

    // Customer 3 - Art Collector
    $stmt->execute([
        4,
        'Youssef Alami',
        '<EMAIL>',
        password_hash('password123', PASSWORD_DEFAULT),
        'customer',
        '+212 6 99 88 77 66',
        '789 Boulevard Zerktouni, Casablanca, Morocco',
        date('Y-m-d H:i:s')
    ]);
    echo "✅ Customer account created: <EMAIL> / password123\n";

    // Customer 4 - Student
    $stmt->execute([
        5,
        'Salma Idrissi',
        '<EMAIL>',
        password_hash('password123', PASSWORD_DEFAULT),
        'customer',
        '+212 6 33 44 55 66',
        '321 Rue Allal Ben Abdellah, Fes, Morocco',
        date('Y-m-d H:i:s')
    ]);
    echo "✅ Customer account created: <EMAIL> / password123\n";

    // Customer 5 - Professional
    $stmt->execute([
        6,
        'Omar Tazi',
        '<EMAIL>',
        password_hash('password123', PASSWORD_DEFAULT),
        'customer',
        '+212 6 77 88 99 00',
        '654 Avenue Mohammed VI, Tangier, Morocco',
        date('Y-m-d H:i:s')
    ]);
    echo "✅ Customer account created: <EMAIL> / password123\n";

    // Display all users
    echo "\n📋 All User Accounts:\n";
    echo "==========================================\n";

    $stmt = $pdo->query("SELECT id, name, email, role, phone, address FROM users ORDER BY id");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($users as $user) {
        echo "ID: {$user['id']}\n";
        echo "Name: {$user['name']}\n";
        echo "Email: {$user['email']}\n";
        echo "Role: {$user['role']}\n";
        echo "Phone: {$user['phone']}\n";
        echo "Address: {$user['address']}\n";
        echo "Password: password123 (for customers) / admin123 (for admin)\n";
        echo "==========================================\n";
    }

    echo "\n🎉 User accounts created successfully!\n";
    echo "You can now test login functionality with these accounts.\n";

} catch (PDOException $e) {
    echo "❌ Error creating users: " . $e->getMessage() . "\n";
}
?>
