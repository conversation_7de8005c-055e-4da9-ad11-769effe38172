/* Import Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 24posters.co Inspired Color Palette */
:root {
  --primary-dark: #0b090a;      /* deepest black */
  --secondary-dark: #161a1d;    /* dark gray */
  --accent-red-dark: #660708;   /* deep red */
  --accent-red-medium: #a4161a; /* medium red */
  --accent-red: #ba181b;        /* primary red */
  --accent-red-light: #e5383b;  /* bright red */
  --neutral-medium: #b1a7a6;    /* warm gray */
  --neutral-light: #d3d3d3;     /* light gray */
  --background-light: #f5f3f4;  /* off-white */
  --pure-white: #ffffff;        /* pure white */
}

/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(180deg, var(--primary-dark), var(--secondary-dark));
  color: var(--pure-white);
  overflow-x: hidden;
}

/* Header with 24posters.co Styling */
header {
  background-color: rgba(11, 9, 10, 0.95);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--secondary-dark);
}

header h1 {
  font-size: 2rem;
  color: var(--accent-red-light);
  letter-spacing: 2px;
}

nav {
  display: flex;
  align-items: center;
}

nav a {
  color: #ffffff;
  text-decoration: none;
  margin: 0 15px;
  font-size: 1.1rem;
  transition: color 0.3s;
}

nav a:hover {
  color: #ffeded;
  font-size: 1.3rem;
}

.logo {
  height: auto;
  width: 150px;
}

/* Dropdown Menu */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  cursor: pointer;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #00000081;
  border-radius: 8px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.5);
  z-index: 999;
  min-width: 100px;
  padding: 10px 0;
  color: #ffffff;
}

.dropdown-menu a {
  display: block;
  padding: 10px 20px;
  text-decoration: none;
  color: #f2f2f2;
  font-size: 10px;
  transition: background-color 0.3s ease, color 0.3s ease;
}


/* Show Dropdown on Hover */
.dropdown:hover .dropdown-menu {
  display: block;
}

/* Hero Section - Background Image with Custom Gradient Overlay */
.hero-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-image: url("./posters image/bg poster.jpg");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000000;
  background: linear-gradient(90deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) 26%, rgba(0, 0, 0, 1) 38%, rgba(38, 38, 38, 1) 48%, rgba(166, 166, 166, 1) 79%, rgba(241, 241, 241, 1) 97%, rgba(255, 255, 255, 1) 100%);
  z-index: 1;
}

/* Removed overlay and floating elements for clean gradient background */

.hero-content-left {
  position: relative;
  z-index: 2;
  text-align: left;
  max-width: 500px;
  padding: 0 60px;
  margin-left: 0;
}

.hero-quote {
  font-size: 3.2rem;
  color: #ffffff;
  line-height: 1.2;
  margin: 0;
  font-weight: 700;
  font-family: 'Arial', 'Helvetica', sans-serif;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 0.5px;
  font-style: normal;
}

/* Button styles removed since we're not using buttons anymore */

/* Section Styling with 24posters.co Colors */
.posters-section {
  padding: 80px 0;
  background: linear-gradient(180deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
  position: relative;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--pure-white);
  margin-bottom: 1rem;
  text-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
  font-family: 'Poppins', sans-serif;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--neutral-medium);
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Poster Grid */
.poster-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 50px 20px;
}

.poster-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  background: var(--secondary-dark);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6);
  text-align: center;
  padding: 20px;
  border: 1px solid rgba(177, 167, 166, 0.1);
}

.poster-item:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
}

.poster-item img {
  width: 100%;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.poster-item:hover img {
  transform: scale(1.1);
}

.poster-item p {
  margin-top: 15px;
  font-size: 1.2rem;
  color: var(--neutral-light);
  font-family: 'Poppins', sans-serif;
}

/* Price style */
.poster-item .price {
  font-size: 1.3rem;
  font-weight: bold;
  color: var(--pure-white);
  margin-top: 10px;
}

/* Add-to-cart button */
.add-to-cart {
  margin-top: 15px;
  background: var(--accent-red);
  color: var(--pure-white);
  border: none;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: bold;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(186, 24, 27, 0.3);
}

.add-to-cart:hover {
  background: var(--accent-red-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(186, 24, 27, 0.4);
}

/* Footer Styles */
footer {
  font-family: 'Poppins', sans-serif;
  color: var(--pure-white);
  border-top: 1px solid var(--secondary-dark);
}

.footer-full {
  background-color: var(--primary-dark);
  padding: 20px 0;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section {
  flex: 1;
  margin: 10px 20px;
  min-width: 250px;
}

.footer-section h3 {
  color: #e60000;
  margin-bottom: 15px;
  font-size: 18px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  color: #f5f5f7;
  text-decoration: none;
}

.footer-section ul li {
  margin: 5px 0;
}
.footer-section ul li a {

  color: #f5f5f7;
  text-decoration: none;
}



/* Social Media Links */
.footer-section.social a {
  display: flex;
  align-items: center;
  margin: 10px 0;
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  text-decoration: none;
}


/* Footer Bottom */
.footer-bottom {
  text-align: center;
  padding: 10px 0;
  border-top: 1px solid #ffffff;
  margin-top: 20px;
  font-size: 12px;
}

.footer-bottom p {
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content-left {
    padding: 0 30px;
    max-width: 90%;
  }

  .hero-quote {
    font-size: 2.2rem;
    line-height: 1.1;
    font-weight: 700;
  }

  .section-title {
    font-size: 2.2rem;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-section {
    margin: 20px 0;
  }
}

@media (max-width: 480px) {
  .hero-content-left {
    padding: 0 20px;
    max-width: 95%;
  }

  .hero-quote {
    font-size: 1.8rem;
    line-height: 1.1;
    letter-spacing: 0.3px;
    font-weight: 700;
  }
}
.poster-slider {
  width: 80%;
  margin: 0 auto;
  padding: 20px;
}

.poster-item {
  text-align: center;
}

.poster-slider img {
  width: 100%;
  height: auto;
  max-width: 250px; /* Adjust the image size */
  margin: 0 auto;
}

.price {
  font-size: 16px;
  color: #333;
}

.add-to-cart {
  background-color: #ffffff;
  color: rgb(0, 0, 0);
  border: none;
  padding: 10px;
  cursor: pointer;
}

.add-to-cart:hover {
  background-color:#000000;
  color: #ffffff;
}

