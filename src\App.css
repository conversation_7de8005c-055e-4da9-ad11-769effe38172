/* Tailwind CSS temporarily disabled to fix compilation issues */
/* @tailwind base;
@tailwind components;
@tailwind utilities; */

/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif; /* Sleek modern font */
  background: linear-gradient(180deg, #0f0f0f, #1c1c1e);
  color: #ffffff;
  overflow-x: hidden;
}

/* Header with Cinematic Styling */
header {
  background-color: rgba(15, 15, 15, 0.9);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.6);
}

header h1 {
  font-size: 2rem;
  color: #e60026; /* Oscar gold */
  letter-spacing: 2px;
}

nav {
  display: flex;
  align-items: center;
}

nav a {
  color: #ffffff;
  text-decoration: none;
  margin: 0 15px;
  font-size: 1.1rem;
  transition: color 0.3s;
}

nav a:hover {
  color: #ffeded;
  font-size: 1.3rem;
}

.logo {
  height: auto;
  width: 150px;
}

/* Dropdown Menu */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  cursor: pointer;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #00000081;
  border-radius: 8px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.5);
  z-index: 999;
  min-width: 100px;
  padding: 10px 0;
  color: #ffffff;
}

.dropdown-menu a {
  display: block;
  padding: 10px 20px;
  text-decoration: none;
  color: #f2f2f2;
  font-size: 10px;
  transition: background-color 0.3s ease, color 0.3s ease;
}


/* Show Dropdown on Hover */
.dropdown:hover .dropdown-menu {
  display: block;
}

/* Hero Section */
.hero {
  height: 100vh;
  display: flex;
  background-image: url("./posters image/bg poster.jpg");
  position: relative;
}

.heroDiv {
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 20px;
  width: 60%;
  border-radius: 4%;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0.8596230728619573) 45%, rgba(255, 255, 255, 0.10892279274991246) 90%, rgba(255, 255, 255, 0) 100%);
}

.hero h2 {
  position: relative;
  font-size: 3.5rem;
  color: #ffffff;
  text-align: center;
  text-shadow: 0px 4px 15px rgba(0, 0, 0, 0.7);
  font-family: 'Roboto', sans-serif;
}

.section-title {
  margin-top: 100px;
  text-align: center;
  font-size: 23px;
  font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;

  color: rgb(255, 255, 255);
}

/* Poster Grid */
.poster-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 50px 20px;
}

.poster-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  background: linear-gradient(145deg, #1c1c1e, #29292b); /* Gradient sombre élégant */
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.4);
  text-align: center;
  padding: 20px;
}

.poster-item:hover {
  transform: scale(1.05); /* Zoom léger */
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.6);
}

.poster-item img {
  width: 100%;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.poster-item:hover img {
  transform: scale(1.1);
}

.poster-item p {
  margin-top: 15px;
  font-size: 1.2rem;
  color: #f5f5f7;
  font-family: 'Times New Roman', Times, serif;
}

/* Price style */
.poster-item .price {
  font-size: 1.3rem;
  font-weight: bold;
  color: #ffffff; /* Couleur dorée pour le prix */
  margin-top: 10px;
}

/* Add-to-cart button */
.add-to-cart {
  margin-top: 15px;
  background: linear-gradient(90deg, #e7d9db); /* Dégradé doré */
  color: #1c1c1e;
  border: none;
  padding: 10px 20px;
  font-size: 1rem;
  font-weight: bold;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease, transform 0.3s ease;
}

.add-to-cart:hover {
  background: #1c1c1e;
  color: #e7d9db;
}

/* Footer Styles */
footer {
  font-family: 'Open Sans', sans-serif;
  color: #fff;
  border: solid rgb(132, 131, 131) 2px;
}

.footer-full {
  background-color: #111; /* Dark background */
  padding: 20px 0;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section {
  flex: 1;
  margin: 10px 20px;
  min-width: 250px;
}

.footer-section h3 {
  color: #e60000;
  margin-bottom: 15px;
  font-size: 18px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  color: #f5f5f7;
  text-decoration: none;
}

.footer-section ul li {
  margin: 5px 0;
}
.footer-section ul li a {

  color: #f5f5f7;
  text-decoration: none;
}



/* Social Media Links */
.footer-section.social a {
  display: flex;
  align-items: center;
  margin: 10px 0;
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  text-decoration: none;
}


/* Footer Bottom */
.footer-bottom {
  text-align: center;
  padding: 10px 0;
  border-top: 1px solid #ffffff;
  margin-top: 20px;
  font-size: 12px;
}

.footer-bottom p {
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-section {
    margin: 20px 0;
  }
}
.poster-slider {
  width: 80%;
  margin: 0 auto;
  padding: 20px;
}

.poster-item {
  text-align: center;
}

.poster-slider img {
  width: 100%;
  height: auto;
  max-width: 250px; /* Adjust the image size */
  margin: 0 auto;
}

.price {
  font-size: 16px;
  color: #333;
}

.add-to-cart {
  background-color: #ffffff;
  color: rgb(0, 0, 0);
  border: none;
  padding: 10px;
  cursor: pointer;
}

.add-to-cart:hover {
  background-color:#000000;
  color: #ffffff;
}

