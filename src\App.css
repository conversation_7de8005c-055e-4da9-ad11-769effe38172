/* Tailwind CSS temporarily disabled to fix compilation issues */
/* @tailwind base;
@tailwind components;
@tailwind utilities; */

/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif; /* Sleek modern font */
  background: linear-gradient(180deg, #0f0f0f, #1c1c1e);
  color: #ffffff;
  overflow-x: hidden;
}

/* Header with Cinematic Styling */
header {
  background-color: rgba(15, 15, 15, 0.9);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.6);
}

header h1 {
  font-size: 2rem;
  color: #e60026; /* Oscar gold */
  letter-spacing: 2px;
}

nav {
  display: flex;
  align-items: center;
}

nav a {
  color: #ffffff;
  text-decoration: none;
  margin: 0 15px;
  font-size: 1.1rem;
  transition: color 0.3s;
}

nav a:hover {
  color: #ffeded;
  font-size: 1.3rem;
}

.logo {
  height: auto;
  width: 150px;
}

/* Dropdown Menu */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  cursor: pointer;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #00000081;
  border-radius: 8px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.5);
  z-index: 999;
  min-width: 100px;
  padding: 10px 0;
  color: #ffffff;
}

.dropdown-menu a {
  display: block;
  padding: 10px 20px;
  text-decoration: none;
  color: #f2f2f2;
  font-size: 10px;
  transition: background-color 0.3s ease, color 0.3s ease;
}


/* Show Dropdown on Hover */
.dropdown:hover .dropdown-menu {
  display: block;
}

/* Hero Section - Enhanced with Beautiful Background */
.hero-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("./posters image/poster1.jpg");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.7) 30%,
    rgba(15, 15, 15, 0.8) 60%,
    rgba(0, 0, 0, 0.9) 100%);
  z-index: 1;
}

/* Floating background elements */
.hero-section::after {
  content: '';
  position: absolute;
  top: 20%;
  left: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(230, 0, 38, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
  z-index: 1;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.hero-message {
  margin-bottom: 3rem;
}

/* Remove the I-POSTER title styles since we're not using it anymore */

.hero-subtitle {
  font-size: 2rem;
  color: #ffffff;
  line-height: 1.6;
  margin-bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(15px);
  padding: 30px 40px;
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 30px rgba(230, 0, 38, 0.1);
  font-weight: 400;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.7);
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
}

@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
    justify-content: center;
    gap: 2rem;
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 200px;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #e60026 0%, #b8001f 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #ff0030 0%, #e60026 100%);
  transform: translateY(-3px);
  box-shadow:
    0 12px 35px rgba(230, 0, 38, 0.4),
    0 6px 15px rgba(0, 0, 0, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #3a3a3a 0%, #2a2a2a 100%);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-3px);
  box-shadow:
    0 12px 35px rgba(255, 255, 255, 0.1),
    0 6px 15px rgba(0, 0, 0, 0.3);
}

/* Section Styling */
.posters-section {
  padding: 80px 0;
  background: linear-gradient(180deg, #0f0f0f 0%, #1a1a1a 100%);
  position: relative;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
  text-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
  font-family: 'Poppins', sans-serif;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #b0b0b0;
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Poster Grid */
.poster-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 50px 20px;
}

.poster-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  background: linear-gradient(145deg, #1c1c1e, #29292b); /* Gradient sombre élégant */
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.4);
  text-align: center;
  padding: 20px;
}

.poster-item:hover {
  transform: scale(1.05); /* Zoom léger */
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.6);
}

.poster-item img {
  width: 100%;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.poster-item:hover img {
  transform: scale(1.1);
}

.poster-item p {
  margin-top: 15px;
  font-size: 1.2rem;
  color: #f5f5f7;
  font-family: 'Times New Roman', Times, serif;
}

/* Price style */
.poster-item .price {
  font-size: 1.3rem;
  font-weight: bold;
  color: #ffffff; /* Couleur dorée pour le prix */
  margin-top: 10px;
}

/* Add-to-cart button */
.add-to-cart {
  margin-top: 15px;
  background: linear-gradient(90deg, #e7d9db); /* Dégradé doré */
  color: #1c1c1e;
  border: none;
  padding: 10px 20px;
  font-size: 1rem;
  font-weight: bold;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease, transform 0.3s ease;
}

.add-to-cart:hover {
  background: #1c1c1e;
  color: #e7d9db;
}

/* Footer Styles */
footer {
  font-family: 'Open Sans', sans-serif;
  color: #fff;
  border: solid rgb(132, 131, 131) 2px;
}

.footer-full {
  background-color: #111; /* Dark background */
  padding: 20px 0;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section {
  flex: 1;
  margin: 10px 20px;
  min-width: 250px;
}

.footer-section h3 {
  color: #e60000;
  margin-bottom: 15px;
  font-size: 18px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  color: #f5f5f7;
  text-decoration: none;
}

.footer-section ul li {
  margin: 5px 0;
}
.footer-section ul li a {

  color: #f5f5f7;
  text-decoration: none;
}



/* Social Media Links */
.footer-section.social a {
  display: flex;
  align-items: center;
  margin: 10px 0;
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  text-decoration: none;
}


/* Footer Bottom */
.footer-bottom {
  text-align: center;
  padding: 10px 0;
  border-top: 1px solid #ffffff;
  margin-top: 20px;
  font-size: 12px;
}

.footer-bottom p {
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-subtitle {
    font-size: 1.4rem;
    padding: 20px 25px;
    margin-bottom: 2rem;
  }

  .btn {
    min-width: 180px;
    padding: 12px 25px;
    font-size: 1rem;
  }

  .section-title {
    font-size: 2.2rem;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-section {
    margin: 20px 0;
  }
}

@media (max-width: 480px) {
  .hero-subtitle {
    font-size: 1.1rem;
    padding: 15px 20px;
  }

  .hero-content {
    padding: 0 15px;
  }
}
.poster-slider {
  width: 80%;
  margin: 0 auto;
  padding: 20px;
}

.poster-item {
  text-align: center;
}

.poster-slider img {
  width: 100%;
  height: auto;
  max-width: 250px; /* Adjust the image size */
  margin: 0 auto;
}

.price {
  font-size: 16px;
  color: #333;
}

.add-to-cart {
  background-color: #ffffff;
  color: rgb(0, 0, 0);
  border: none;
  padding: 10px;
  cursor: pointer;
}

.add-to-cart:hover {
  background-color:#000000;
  color: #ffffff;
}

