<?php

/**
 * Test script for I-POSTER API
 */

echo "🧪 Testing I-POSTER API Endpoints\n";
echo "================================\n\n";

// Test 1: Get all posters
echo "1. Testing GET /api/posters\n";
$response = file_get_contents('http://localhost:8000/api/posters');
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "✅ SUCCESS: Found " . $data['total'] . " posters\n";
    if (!empty($data['data'])) {
        $poster = $data['data'][0];
        echo "   Sample poster: " . $poster['title'] . " - $" . $poster['price'] . "\n";
    }
} else {
    echo "❌ FAILED: " . ($data['error'] ?? 'Unknown error') . "\n";
}
echo "\n";

// Test 2: Get music posters
echo "2. Testing GET /api/posters?category=music\n";
$response = file_get_contents('http://localhost:8000/api/posters?category=music');
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "✅ SUCCESS: Found " . $data['total'] . " music posters\n";
} else {
    echo "❌ FAILED: " . ($data['error'] ?? 'Unknown error') . "\n";
}
echo "\n";

// Test 3: Get cart (should be empty initially)
echo "3. Testing GET /api/cart\n";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'X-Session-ID: test_session_123'
    ]
]);
$response = file_get_contents('http://localhost:8000/api/cart', false, $context);
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "✅ SUCCESS: Cart retrieved with " . $data['data']['count'] . " items\n";
    echo "   Total: $" . $data['data']['total'] . "\n";
} else {
    echo "❌ FAILED: " . ($data['error'] ?? 'Unknown error') . "\n";
}
echo "\n";

// Test 4: Add item to cart
echo "4. Testing POST /api/cart/add\n";
$postData = json_encode(['poster_id' => 1, 'quantity' => 2]);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'X-Session-ID: test_session_123'
        ],
        'content' => $postData
    ]
]);
$response = file_get_contents('http://localhost:8000/api/cart/add', false, $context);
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "✅ SUCCESS: Item added to cart\n";
    echo "   Message: " . $data['message'] . "\n";
} else {
    echo "❌ FAILED: " . ($data['error'] ?? 'Unknown error') . "\n";
}
echo "\n";

// Test 5: Get cart again (should have items now)
echo "5. Testing GET /api/cart (after adding item)\n";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'X-Session-ID: test_session_123'
    ]
]);
$response = file_get_contents('http://localhost:8000/api/cart', false, $context);
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "✅ SUCCESS: Cart retrieved with " . $data['data']['count'] . " items\n";
    echo "   Total: $" . $data['data']['total'] . "\n";
    if (!empty($data['data']['items'])) {
        $item = $data['data']['items'][0];
        echo "   First item: " . $item['title'] . " (Qty: " . $item['quantity'] . ")\n";
    }
} else {
    echo "❌ FAILED: " . ($data['error'] ?? 'Unknown error') . "\n";
}
echo "\n";

// Test 6: Test user registration
echo "6. Testing POST /api/auth/register\n";
$userData = json_encode([
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'phone' => '+212600000000'
]);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $userData
    ]
]);
$response = file_get_contents('http://localhost:8000/api/auth/register', false, $context);
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "✅ SUCCESS: User registered successfully\n";
    echo "   User: " . $data['data']['user']['name'] . " (" . $data['data']['user']['email'] . ")\n";
} else {
    echo "❌ FAILED: " . ($data['error'] ?? 'Unknown error') . "\n";
}
echo "\n";

// Test 7: Test user login
echo "7. Testing POST /api/auth/login\n";
$loginData = json_encode([
    'email' => '<EMAIL>',
    'password' => 'password123'
]);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $loginData
    ]
]);
$response = file_get_contents('http://localhost:8000/api/auth/login', false, $context);
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "✅ SUCCESS: Admin login successful\n";
    echo "   User: " . $data['data']['user']['name'] . " (Role: " . $data['data']['user']['role'] . ")\n";
    $adminToken = $data['data']['token'];
} else {
    echo "❌ FAILED: " . ($data['error'] ?? 'Unknown error') . "\n";
    $adminToken = null;
}
echo "\n";

// Test 8: Create order
echo "8. Testing POST /api/orders\n";
$orderData = json_encode([
    'customer_name' => 'John Doe',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '+212600123456',
    'delivery_address' => '123 Main St, Casablanca, Morocco',
    'notes' => 'Please deliver in the evening'
]);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'X-Session-ID: test_session_123'
        ],
        'content' => $orderData
    ]
]);
$response = file_get_contents('http://localhost:8000/api/orders', false, $context);
$data = json_decode($response, true);

if ($data && $data['success']) {
    echo "✅ SUCCESS: Order created successfully\n";
    echo "   Order Number: " . $data['data']['order_number'] . "\n";
    echo "   Total: $" . $data['data']['total_amount'] . "\n";
} else {
    echo "❌ FAILED: " . ($data['error'] ?? 'Unknown error') . "\n";
}
echo "\n";

echo "🎉 API Testing Complete!\n";
echo "========================\n";
echo "Your I-POSTER backend is ready for production!\n\n";

echo "📋 Available Endpoints:\n";
echo "- GET  /api/posters (get all posters)\n";
echo "- GET  /api/posters?category=music (get music posters)\n";
echo "- GET  /api/cart (get cart contents)\n";
echo "- POST /api/cart/add (add item to cart)\n";
echo "- POST /api/auth/register (register user)\n";
echo "- POST /api/auth/login (login user)\n";
echo "- POST /api/orders (create order)\n";
echo "- GET  /api/orders (get orders)\n";

?>
