import React, { useState, useEffect } from "react";
import { postersAPI } from "../services/api";
import PosterCard from "./PosterCard";

function MusicPosters() {
  const [posters, setPosters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchMusicPosters();
  }, []);

  const fetchMusicPosters = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the correct API endpoint
      const response = await postersAPI.getAll({ category: 'music' });
      console.log('API Response:', response.data); // Debug log

      if (response.data.success && response.data.data) {
        setPosters(response.data.data);
      } else {
        setPosters([]);
        setError('No music posters found');
      }
    } catch (error) {
      console.error('Error fetching music posters:', error);
      setError(`Failed to load music posters: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Error</h2>
          <p className="text-gray-400">{error}</p>
          <button
            onClick={fetchMusicPosters}
            className="mt-4 bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🎵 MUSIC POSTERS
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Discover underrated musical masterpieces. From experimental hip-hop to indie folk gems,
            find the perfect poster to express your unique taste in music.
          </p>
        </div>

        {/* Posters Grid */}
        {posters.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {posters.map((poster) => (
              <PosterCard key={poster.id} poster={poster} />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <h3 className="text-2xl font-semibold text-white mb-4">
              No Music Posters Found
            </h3>
            <p className="text-gray-400">
              Check back soon for new additions to our music collection!
            </p>
          </div>
        )}

        {/* Featured Section */}
        <div className="mt-16 bg-gray-800 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-white mb-4">
            Why These Albums Are Underrated
          </h2>
          <div className="grid md:grid-cols-2 gap-6 text-gray-300">
            <div>
              <h3 className="font-semibold text-white mb-2">Frank Ocean - Blonde</h3>
              <p className="text-sm">
                A masterpiece of modern R&B that redefined the genre with its experimental approach and emotional depth.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">Madvillain - Madvillainy</h3>
              <p className="text-sm">
                MF DOOM and Madlib's collaboration created one of hip-hop's most innovative and influential albums.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">Neutral Milk Hotel</h3>
              <p className="text-sm">
                A cult classic that influenced countless indie bands with its unique blend of folk and psychedelic sounds.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">Flying Lotus - Cosmogramma</h3>
              <p className="text-sm">
                An experimental electronic masterpiece that bridges jazz, hip-hop, and ambient music.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MusicPosters;
