import './App.css';
import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Header from "./header";
import Hero from "./Hero";
import MusicPosters from "./MusicPosters";
import MoviesPosters from "./MoviesPosters";
import Footer from "./Footer";
import Signup from './Signup';

function App() {
  return (
    <BrowserRouter>
      <div>
        <Header />
        <Routes>
          <Route path="/" element={<Hero />} />
          <Route path="/music-posters" element={<MusicPosters />} />
          <Route path="/movies-posters" element={<MoviesPosters />} />
          <Route path="/Signup" element={<Signup />} />
        </Routes>
        <Footer />
      </div>
    </BrowserRouter>
  );
}

export default App;
