import './App.css';
import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { CartProvider } from "./contexts/CartContext";
import Header from "./components/Header";
import Hero from "./components/Hero";
import MusicPosters from "./components/MusicPosters";
import MoviesPosters from "./components/MoviesPosters";
import AlbumPosters from "./components/AlbumPosters";
import FeaturedPosters from "./components/FeaturedPosters";
import Footer from "./components/Footer";
import Signup from './components/Signup';
import Login from './components/Login';
import Cart from './components/Cart';
import Checkout from './components/Checkout';
import Profile from './components/Profile';
import Orders from './components/Orders';
import OrderDetails from './components/OrderDetails';
import AdminDashboard from './components/admin/AdminDashboard';
import AdminPosters from './components/admin/AdminPosters';
import AdminOrders from './components/admin/AdminOrders';
import AdminUsers from './components/admin/AdminUsers';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';

function App() {
  return (
    <AuthProvider>
      <CartProvider>
        <BrowserRouter>
          <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black">
            <Header />
            <main className="pt-20">
              <Routes>
                <Route path="/" element={<Hero />} />
                <Route path="/music-posters" element={<MusicPosters />} />
                <Route path="/album-posters" element={<AlbumPosters />} />
                <Route path="/featured-posters" element={<FeaturedPosters />} />
                <Route path="/movies-posters" element={<MoviesPosters />} />
                <Route path="/signup" element={<Signup />} />
                <Route path="/login" element={<Login />} />
                <Route path="/cart" element={<Cart />} />
                <Route path="/checkout" element={<Checkout />} />

                {/* Protected Routes */}
                <Route path="/profile" element={
                  <ProtectedRoute>
                    <Profile />
                  </ProtectedRoute>
                } />
                <Route path="/orders" element={
                  <ProtectedRoute>
                    <Orders />
                  </ProtectedRoute>
                } />
                <Route path="/orders/:id" element={
                  <ProtectedRoute>
                    <OrderDetails />
                  </ProtectedRoute>
                } />

                {/* Admin Routes */}
                <Route path="/admin" element={
                  <AdminRoute>
                    <AdminDashboard />
                  </AdminRoute>
                } />
                <Route path="/admin/posters" element={
                  <AdminRoute>
                    <AdminPosters />
                  </AdminRoute>
                } />
                <Route path="/admin/orders" element={
                  <AdminRoute>
                    <AdminOrders />
                  </AdminRoute>
                } />
                <Route path="/admin/users" element={
                  <AdminRoute>
                    <AdminUsers />
                  </AdminRoute>
                } />
              </Routes>
            </main>
            <Footer />
          </div>
        </BrowserRouter>
      </CartProvider>
    </AuthProvider>
  );
}

export default App;
