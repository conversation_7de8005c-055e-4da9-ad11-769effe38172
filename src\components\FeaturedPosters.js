import React, { useState, useEffect } from "react";
import { postersAPI } from "../services/api";
import PosterCard from "./PosterCard";

function FeaturedPosters() {
  const [posters, setPosters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeCategory, setActiveCategory] = useState('all');

  useEffect(() => {
    fetchFeaturedPosters();
  }, []);

  const fetchFeaturedPosters = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all posters and filter featured ones
      const response = await postersAPI.getAll({ featured: 1 });
      console.log('Featured API Response:', response.data); // Debug log

      if (response.data.success && response.data.data) {
        setPosters(response.data.data);
      } else {
        setPosters([]);
        setError('No featured posters found');
      }
    } catch (error) {
      console.error('Error fetching featured posters:', error);
      setError(`Failed to load featured posters: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Filter posters by category
  const filteredPosters = activeCategory === 'all'
    ? posters
    : posters.filter(poster => poster.category === activeCategory);

  // Get category counts
  const categoryCounts = {
    all: posters.length,
    music: posters.filter(p => p.category === 'music').length,
    album: posters.filter(p => p.category === 'album').length,
    movie: posters.filter(p => p.category === 'movie').length
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-primary-dark to-secondary-dark flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-red"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-primary-dark to-secondary-dark flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-pure-white mb-4">Error</h2>
          <p className="text-neutral-medium">{error}</p>
          <button
            onClick={fetchFeaturedPosters}
            className="mt-4 bg-accent-red hover:bg-accent-red-dark text-pure-white px-6 py-2 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-primary-dark to-secondary-dark py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-pure-white mb-4">
            ⭐ FEATURED POSTERS
          </h1>
          <p className="text-neutral-medium text-lg max-w-2xl mx-auto">
            Our handpicked selection of the most iconic and beloved posters.
            These are the cream of the crop - the posters that define culture and inspire generations.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          <button
            onClick={() => setActiveCategory('all')}
            className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 shadow-lg ${
              activeCategory === 'all'
                ? 'bg-accent-red text-pure-white shadow-xl'
                : 'bg-secondary-dark text-neutral-light hover:bg-primary-dark border border-neutral-medium/20'
            }`}
          >
            All Featured ({categoryCounts.all})
          </button>
          {categoryCounts.music > 0 && (
            <button
              onClick={() => setActiveCategory('music')}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 shadow-lg ${
                activeCategory === 'music'
                  ? 'bg-accent-red text-pure-white shadow-xl'
                  : 'bg-secondary-dark text-neutral-light hover:bg-primary-dark border border-neutral-medium/20'
              }`}
            >
              Music ({categoryCounts.music})
            </button>
          )}
          {categoryCounts.album > 0 && (
            <button
              onClick={() => setActiveCategory('album')}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                activeCategory === 'album'
                  ? 'bg-red-600 text-white shadow-lg'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              Albums ({categoryCounts.album})
            </button>
          )}
          {categoryCounts.movie > 0 && (
            <button
              onClick={() => setActiveCategory('movie')}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                activeCategory === 'movie'
                  ? 'bg-red-600 text-white shadow-lg'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              Movies ({categoryCounts.movie})
            </button>
          )}
        </div>

        {/* Featured Badge */}
        <div className="bg-gradient-to-r from-yellow-600 to-yellow-500 rounded-lg p-6 mb-12 text-center">
          <div className="flex items-center justify-center mb-3">
            <span className="text-3xl mr-2">🏆</span>
            <h2 className="text-2xl font-bold text-white">Premium Collection</h2>
          </div>
          <p className="text-yellow-100">
            These posters have been carefully selected for their cultural significance,
            artistic merit, and enduring popularity. Each one tells a story and makes a statement.
          </p>
        </div>

        {/* Posters Grid */}
        {filteredPosters.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {filteredPosters.map((poster) => (
              <div key={poster.id} className="relative">
                {/* Featured Badge */}
                <div className="absolute top-2 left-2 z-10 bg-yellow-500 text-black px-2 py-1 rounded-full text-xs font-bold">
                  ⭐ FEATURED
                </div>
                <PosterCard poster={poster} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <h3 className="text-2xl font-semibold text-white mb-4">
              No Featured Posters Found
            </h3>
            <p className="text-gray-400">
              {activeCategory === 'all'
                ? 'Check back soon for new featured additions!'
                : `No featured ${activeCategory} posters available at the moment.`
              }
            </p>
          </div>
        )}

        {/* Why Featured Section */}
        <div className="mt-16 bg-gray-800 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">
            Why These Posters Are Featured
          </h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-4xl mb-3">🎨</div>
              <h3 className="font-semibold text-white mb-2">Iconic Design</h3>
              <p className="text-gray-300 text-sm">
                Instantly recognizable artwork that has become part of popular culture
              </p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-3">📈</div>
              <h3 className="font-semibold text-white mb-2">Cultural Impact</h3>
              <p className="text-gray-300 text-sm">
                Posters that influenced generations and shaped artistic movements
              </p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-3">💎</div>
              <h3 className="font-semibold text-white mb-2">Premium Quality</h3>
              <p className="text-gray-300 text-sm">
                High-quality reproductions that do justice to the original artwork
              </p>
            </div>
          </div>
        </div>

        {/* Collection Stats */}
        <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-red-900 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">{categoryCounts.all}</div>
            <div className="text-red-200 text-sm">Total Featured</div>
          </div>
          <div className="bg-blue-900 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">{categoryCounts.music}</div>
            <div className="text-blue-200 text-sm">Music</div>
          </div>
          <div className="bg-purple-900 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">{categoryCounts.album}</div>
            <div className="text-purple-200 text-sm">Albums</div>
          </div>
          <div className="bg-green-900 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">{categoryCounts.movie}</div>
            <div className="text-green-200 text-sm">Movies</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FeaturedPosters;
