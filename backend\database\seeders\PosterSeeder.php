<?php

namespace Database\Seeders;

use App\Models\Poster;
use Illuminate\Database\Seeder;

class PosterSeeder extends Seeder
{
    public function run(): void
    {
        // Music posters
        Poster::create([
            'title' => 'Flower Boy',
            'description' => '<PERSON> the Creator - Flower Boy',
            'price' => 100.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/poster1.jpg',
            'category' => 'music',
            'artist' => '<PERSON> the Creator',
            'size' => 'A3',
            'frame_type' => 'black',
            'tags' => ['hip-hop', 'album', 'colorful'],
            'featured' => true,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Utopia',
            'description' => 'Travis Scott - Utopia',
            'price' => 99.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/poster2.jpg',
            'category' => 'music',
            'artist' => 'Travis Scott',
            'size' => 'A3',
            'frame_type' => 'none',
            'tags' => ['hip-hop', 'album', 'dark'],
            'featured' => true,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Scorpion',
            'description' => 'Drake - Scorpion',
            'price' => 180.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/poster2.png',
            'category' => 'music',
            'artist' => 'Drake',
            'size' => 'A2',
            'frame_type' => 'gold',
            'tags' => ['hip-hop', 'album', 'premium'],
            'featured' => false,
            'status' => 'active'
        ]);

        // Movie posters
        Poster::create([
            'title' => 'Men in Black',
            'description' => 'Men in Black (1997)',
            'price' => 199.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/men_in_black.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A2',
            'frame_type' => 'black',
            'tags' => ['sci-fi', 'action', 'comedy', '90s'],
            'featured' => true,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Peaky Blinders',
            'description' => 'Peaky Blinders (2013)',
            'price' => 50.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/peaky_blinders.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'none',
            'tags' => ['drama', 'crime', 'series', 'british'],
            'featured' => false,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'The Godfather',
            'description' => 'The Godfather (1972)',
            'price' => 250.00,
            'quantity' => 5,
            'image_path' => '/storage/posters/godfather.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A1',
            'frame_type' => 'wood',
            'tags' => ['classic', 'crime', 'drama', 'vintage'],
            'featured' => true,
            'status' => 'active'
        ]);
    }
}