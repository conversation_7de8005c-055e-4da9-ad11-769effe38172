<?php

namespace Database\Seeders;

use App\Models\Poster;
use Illuminate\Database\Seeder;

class PosterSeeder extends Seeder
{
    public function run(): void
    {
        // UNDERRATED MUSIC POSTERS
        Poster::create([
            'title' => 'Blonde',
            'description' => '<PERSON> Blonde (2016)',
            'price' => 120.00,
            'quantity' => 15,
            'image_path' => '/storage/posters/frank_ocean_blonde.jpg',
            'category' => 'music',
            'artist' => 'Frank Ocean',
            'size' => 'A3',
            'frame_type' => 'white',
            'tags' => ['r&b', 'alternative', 'minimalist', 'underrated'],
            'featured' => true,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Madvillainy',
            'description' => 'Madvillain - Madvillainy (2004)',
            'price' => 95.00,
            'quantity' => 12,
            'image_path' => '/storage/posters/madvillainy.jpg',
            'category' => 'music',
            'artist' => 'Madvillain',
            'size' => 'A3',
            'frame_type' => 'black',
            'tags' => ['hip-hop', 'underground', 'doom', 'classic'],
            'featured' => true,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'In the Aeroplane Over the Sea',
            'description' => 'Neutral Milk Hotel - In the Aeroplane Over the Sea (1998)',
            'price' => 85.00,
            'quantity' => 8,
            'image_path' => '/storage/posters/neutral_milk_hotel.jpg',
            'category' => 'music',
            'artist' => 'Neutral Milk Hotel',
            'size' => 'A4',
            'frame_type' => 'wood',
            'tags' => ['indie', 'folk', 'psychedelic', 'cult-classic'],
            'featured' => false,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Cosmogramma',
            'description' => 'Flying Lotus - Cosmogramma (2010)',
            'price' => 110.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/flying_lotus.jpg',
            'category' => 'music',
            'artist' => 'Flying Lotus',
            'size' => 'A2',
            'frame_type' => 'black',
            'tags' => ['electronic', 'experimental', 'jazz', 'abstract'],
            'featured' => false,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Spiderland',
            'description' => 'Slint - Spiderland (1991)',
            'price' => 90.00,
            'quantity' => 6,
            'image_path' => '/storage/posters/slint_spiderland.jpg',
            'category' => 'music',
            'artist' => 'Slint',
            'size' => 'A3',
            'frame_type' => 'none',
            'tags' => ['post-rock', 'math-rock', 'influential', 'underground'],
            'featured' => false,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Since I Left You',
            'description' => 'The Avalanches - Since I Left You (2000)',
            'price' => 105.00,
            'quantity' => 9,
            'image_path' => '/storage/posters/avalanches.jpg',
            'category' => 'music',
            'artist' => 'The Avalanches',
            'size' => 'A3',
            'frame_type' => 'white',
            'tags' => ['electronic', 'sample-based', 'plunderphonics', 'unique'],
            'featured' => true,
            'status' => 'active'
        ]);

        // UNDERRATED MOVIE POSTERS
        Poster::create([
            'title' => 'The Fall',
            'description' => 'The Fall (2006) - Tarsem Singh',
            'price' => 130.00,
            'quantity' => 8,
            'image_path' => '/storage/posters/the_fall.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A2',
            'frame_type' => 'gold',
            'tags' => ['fantasy', 'visual-masterpiece', 'underrated', 'artistic'],
            'featured' => true,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Coherence',
            'description' => 'Coherence (2013) - James Ward Byrkit',
            'price' => 95.00,
            'quantity' => 12,
            'image_path' => '/storage/posters/coherence.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'black',
            'tags' => ['sci-fi', 'thriller', 'mind-bending', 'indie'],
            'featured' => true,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'The Man from Earth',
            'description' => 'The Man from Earth (2007) - Richard Schenkman',
            'price' => 80.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/man_from_earth.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'none',
            'tags' => ['drama', 'philosophical', 'low-budget-gem', 'thought-provoking'],
            'featured' => false,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Hunt for the Wilderpeople',
            'description' => 'Hunt for the Wilderpeople (2016) - Taika Waititi',
            'price' => 100.00,
            'quantity' => 15,
            'image_path' => '/storage/posters/hunt_wilderpeople.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'wood',
            'tags' => ['comedy', 'adventure', 'new-zealand', 'heartwarming'],
            'featured' => false,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'The Handmaiden',
            'description' => 'The Handmaiden (2016) - Park Chan-wook',
            'price' => 140.00,
            'quantity' => 7,
            'image_path' => '/storage/posters/handmaiden.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A2',
            'frame_type' => 'black',
            'tags' => ['thriller', 'korean', 'psychological', 'artistic'],
            'featured' => true,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'What We Do in the Shadows',
            'description' => 'What We Do in the Shadows (2014) - Taika Waititi',
            'price' => 85.00,
            'quantity' => 20,
            'image_path' => '/storage/posters/what_we_do_shadows.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'none',
            'tags' => ['comedy', 'horror', 'mockumentary', 'cult'],
            'featured' => false,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'The Nice Guys',
            'description' => 'The Nice Guys (2016) - Shane Black',
            'price' => 90.00,
            'quantity' => 11,
            'image_path' => '/storage/posters/nice_guys.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A3',
            'frame_type' => 'white',
            'tags' => ['comedy', 'crime', 'neo-noir', '70s-style'],
            'featured' => false,
            'status' => 'active'
        ]);

        Poster::create([
            'title' => 'Annihilation',
            'description' => 'Annihilation (2018) - Alex Garland',
            'price' => 125.00,
            'quantity' => 9,
            'image_path' => '/storage/posters/annihilation.jpg',
            'category' => 'movie',
            'artist' => null,
            'size' => 'A2',
            'frame_type' => 'black',
            'tags' => ['sci-fi', 'horror', 'psychological', 'visually-stunning'],
            'featured' => true,
            'status' => 'active'
        ]);
    }
}