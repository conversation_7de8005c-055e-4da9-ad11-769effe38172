<?php

namespace Database\Seeders;

use App\Models\Poster;
use Illuminate\Database\Seeder;

class PosterSeeder extends Seeder
{
    public function run(): void
    {
        // Music posters
        Poster::create([
            'title' => 'Flower Boy',
            'description' => '<PERSON> the Creator - Flower Boy',
            'price' => 100.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/poster1.jpg',
            'category' => 'music',
        ]);
        
        Poster::create([
            'title' => 'Utopia',
            'description' => 'Travis Scott - Utopia',
            'price' => 99.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/poster2.jpg',
            'category' => 'music',
        ]);
        
        Poster::create([
            'title' => 'Scorpion',
            'description' => 'Drake - Scorpion',
            'price' => 180.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/poster2.png',
            'category' => 'music',
        ]);

        // Movie posters
        Poster::create([
            'title' => 'Men in Black',
            'description' => 'Men in Black (1997)',
            'price' => 199.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/men_in_black.jpg',
            'category' => 'movie',
        ]);
        
        Poster::create([
            'title' => 'Peaky Blinders',
            'description' => 'Peaky Blinders (2013)',
            'price' => 50.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/peaky_blinders.jpg',
            'category' => 'movie',
        ]);
    }
}