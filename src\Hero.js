import React from "react";
import { Moviesposter,listposter } from "./list";
import Slider from "react-slick";
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
function MoviesPosters() {
  const settings = {
    infinite: true,            // Loop through the items infinitely
    slidesToShow: 3,           // Show 3 slides at once
    slidesToScroll: 1,         // Scroll 1 slide at a time
    autoplay: true,            // Enable autoplay
    autoplaySpeed: 1000,       // Speed of autoplay
    dots: true,                // Show navigation dots
    arrows: true,              // Show previous/next arrows
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,     // Show 2 slides on medium screens
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,     // Show 1 slide on small screens
        },
      },
    ],
  };

  return (
    <>
    
    <section className="hero">
      <div className="heroDiv">
        <h2>"Bring your walls to life with art that speaks to you. Discover the perfect poster to express your style."</h2>
      </div>
      </section>
      <h1 className="section-title">MUSIC POSTERS</h1>
      <section className="poster-slider">
        <Slider {...settings}>
          {listposter.map((poster, i) => (
            <div className="poster-item" key={i}>
              <img src={poster.image} alt={poster.title} />
              <p>{poster.discription}</p>
              <p className="price">Prix : {poster.price} MAD</p>
              <button className="add-to-cart">Ajouter au panier</button>
            </div>
          ))}
        </Slider>
      </section>
      <h1 className="section-title">MOVIES & SERIES POSTERS</h1>
      <section className="poster-slider">
        <Slider {...settings}>
          {Moviesposter.map((poster, i) => (
            <div className="poster-item" key={i}>
              <img src={poster.image} alt={poster.title} />
              <p>{poster.discription}</p>
              <p className="price">Prix : {poster.price} MAD</p>
              <button className="add-to-cart">Ajouter au panier</button>
            </div>
          ))}
        </Slider>
      </section>
    </>
  );
}

export default MoviesPosters;
