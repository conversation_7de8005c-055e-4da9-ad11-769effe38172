import React from 'react';
import { useParams } from 'react-router-dom';

function OrderDetails() {
  const { id } = useParams();

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            📄 Order Details
          </h1>
          <p className="text-gray-400">Order #{id}</p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="text-center py-16">
            <h3 className="text-xl font-semibold text-white mb-2">Order details coming soon</h3>
            <p className="text-gray-400">
              This feature will be implemented to show detailed order information.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OrderDetails;
