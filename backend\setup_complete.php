<?php

/**
 * Complete Setup Script for I-POSTER E-commerce Platform
 * Sets up database, creates tables, seeds data, and verifies API functionality
 */

echo "🚀 I-POSTER E-commerce Platform Setup\n";
echo "=====================================\n\n";

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    // Step 1: Connect to MySQL
    echo "📡 Step 1: Connecting to MySQL...\n";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to MySQL successfully!\n\n";
    
    // Step 2: Create database
    echo "🗄️  Step 2: Creating database...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database '$database' created/verified!\n\n";
    
    // Step 3: Use the database
    $pdo->exec("USE `$database`");
    
    // Step 4: Create tables
    echo "📋 Step 3: Creating database tables...\n";
    
    // Users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL UNIQUE,
            `email_verified_at` timestamp NULL DEFAULT NULL,
            `password` varchar(255) NOT NULL,
            `phone` varchar(255) NULL,
            `address` text NULL,
            `role` enum('customer','admin') NOT NULL DEFAULT 'customer',
            `remember_token` varchar(100) NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Users table created!\n";
    
    // Posters table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `posters` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text NOT NULL,
            `price` decimal(8,2) NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT 10,
            `image_path` varchar(255) NOT NULL,
            `category` enum('music','movie','art','sports','vintage') NOT NULL,
            `artist` varchar(255) NULL,
            `size` enum('A4','A3','A2','A1','50x70','70x100') NOT NULL DEFAULT 'A4',
            `frame_type` enum('none','black','white','wood','gold') NOT NULL DEFAULT 'none',
            `tags` json NULL,
            `featured` tinyint(1) NOT NULL DEFAULT 0,
            `status` enum('active','inactive','out_of_stock') NOT NULL DEFAULT 'active',
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Posters table created!\n";
    
    // Carts table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `carts` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NULL,
            `session_id` varchar(255) NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `carts_user_id_session_id_index` (`user_id`,`session_id`),
            FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Carts table created!\n";
    
    // Cart items table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `cart_items` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `cart_id` bigint(20) unsigned NOT NULL,
            `poster_id` bigint(20) unsigned NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT 1,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `cart_items_cart_id_poster_id_unique` (`cart_id`,`poster_id`),
            FOREIGN KEY (`cart_id`) REFERENCES `carts` (`id`) ON DELETE CASCADE,
            FOREIGN KEY (`poster_id`) REFERENCES `posters` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Cart items table created!\n";
    
    // Orders table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `orders` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint(20) unsigned NULL,
            `order_number` varchar(255) NOT NULL UNIQUE,
            `customer_name` varchar(255) NOT NULL,
            `customer_email` varchar(255) NULL,
            `customer_phone` varchar(255) NOT NULL,
            `delivery_address` text NOT NULL,
            `notes` text NULL,
            `total_amount` decimal(10,2) NOT NULL,
            `payment_type` varchar(255) NOT NULL DEFAULT 'Cash on Delivery',
            `status` enum('pending','confirmed','shipped','delivered','cancelled') NOT NULL DEFAULT 'pending',
            `admin_notes` text NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `orders_status_created_at_index` (`status`,`created_at`),
            KEY `orders_customer_phone_index` (`customer_phone`),
            FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Orders table created!\n";
    
    // Order items table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `order_items` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `order_id` bigint(20) unsigned NOT NULL,
            `poster_id` bigint(20) unsigned NOT NULL,
            `quantity` int(11) NOT NULL,
            `price` decimal(8,2) NOT NULL,
            `poster_title` varchar(255) NOT NULL,
            `poster_description` text NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
            FOREIGN KEY (`poster_id`) REFERENCES `posters` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Order items table created!\n";
    
    // Personal access tokens table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `personal_access_tokens` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `tokenable_type` varchar(255) NOT NULL,
            `tokenable_id` bigint(20) unsigned NOT NULL,
            `name` varchar(255) NOT NULL,
            `token` varchar(64) NOT NULL UNIQUE,
            `abilities` text NULL,
            `last_used_at` timestamp NULL DEFAULT NULL,
            `expires_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Personal access tokens table created!\n\n";
    
    // Step 5: Seed data
    echo "🌱 Step 4: Seeding initial data...\n";
    
    // Clear existing data
    $pdo->exec("DELETE FROM order_items");
    $pdo->exec("DELETE FROM orders");
    $pdo->exec("DELETE FROM cart_items");
    $pdo->exec("DELETE FROM carts");
    $pdo->exec("DELETE FROM posters");
    $pdo->exec("DELETE FROM users");
    echo "✅ Cleared existing data!\n";
    
    // Create admin user
    $adminPassword = password_hash('password123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (name, email, password, role, created_at, updated_at) 
        VALUES (?, ?, ?, 'admin', NOW(), NOW())
    ");
    $stmt->execute(['Admin User', '<EMAIL>', $adminPassword]);
    echo "✅ Admin user created! (email: <EMAIL>, password: password123)\n";
    
    // Create test customer
    $customerPassword = password_hash('password123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (name, email, password, phone, address, role, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, 'customer', NOW(), NOW())
    ");
    $stmt->execute([
        'Test Customer', 
        '<EMAIL>', 
        $customerPassword,
        '+212600000000',
        '123 Test Street, Casablanca, Morocco'
    ]);
    echo "✅ Test customer created! (email: <EMAIL>, password: password123)\n";
    
    // Add sample posters
    $posters = [
        [
            'title' => 'Blonde',
            'description' => 'Frank Ocean\'s critically acclaimed album featuring introspective lyrics and innovative production.',
            'price' => 120.00,
            'image_path' => '/images/blonde.jpg',
            'category' => 'music',
            'artist' => 'Frank Ocean',
            'featured' => 1
        ],
        [
            'title' => 'To Pimp a Butterfly',
            'description' => 'Kendrick Lamar\'s masterpiece exploring themes of race, politics, and personal growth.',
            'price' => 110.00,
            'image_path' => '/images/tpab.jpg',
            'category' => 'music',
            'artist' => 'Kendrick Lamar',
            'featured' => 1
        ],
        [
            'title' => 'The Godfather',
            'description' => 'Classic crime drama film poster from Francis Ford Coppola\'s masterpiece.',
            'price' => 95.00,
            'image_path' => '/images/godfather.jpg',
            'category' => 'movie',
            'artist' => 'Francis Ford Coppola',
            'featured' => 0
        ],
        [
            'title' => 'Pulp Fiction',
            'description' => 'Iconic Quentin Tarantino film poster with distinctive retro styling.',
            'price' => 85.00,
            'image_path' => '/images/pulp_fiction.jpg',
            'category' => 'movie',
            'artist' => 'Quentin Tarantino',
            'featured' => 1
        ],
        [
            'title' => 'Abbey Road',
            'description' => 'The Beatles\' iconic album cover featuring the famous crosswalk scene.',
            'price' => 130.00,
            'image_path' => '/images/abbey_road.jpg',
            'category' => 'music',
            'artist' => 'The Beatles',
            'featured' => 1
        ],
        [
            'title' => 'Dark Side of the Moon',
            'description' => 'Pink Floyd\'s legendary album artwork with the iconic prism design.',
            'price' => 125.00,
            'image_path' => '/images/dark_side.jpg',
            'category' => 'music',
            'artist' => 'Pink Floyd',
            'featured' => 0
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO posters (
            title, description, price, image_path, category, artist, featured, 
            size, frame_type, tags, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'A4', 'none', '[]', 'active', NOW(), NOW())
    ");
    
    foreach ($posters as $poster) {
        $stmt->execute([
            $poster['title'],
            $poster['description'],
            $poster['price'],
            $poster['image_path'],
            $poster['category'],
            $poster['artist'],
            $poster['featured']
        ]);
    }
    echo "✅ Sample posters added!\n\n";
    
    // Step 6: Verify setup
    echo "🔍 Step 5: Verifying setup...\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ Users: $userCount\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posters");
    $posterCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "✅ Posters: $posterCount\n";
    
    echo "\n🎉 Setup completed successfully!\n";
    echo "================================\n\n";
    
    echo "📋 Account Information:\n";
    echo "👤 Admin: <EMAIL> / password123\n";
    echo "👤 Customer: <EMAIL> / password123\n\n";
    
    echo "🚀 Next Steps:\n";
    echo "1. Start the API server: php -S localhost:8000 api_complete.php\n";
    echo "2. Start the frontend: npm start\n";
    echo "3. Test the API: php test_api.php\n";
    echo "4. Visit: http://localhost:3000\n\n";
    
    echo "📚 Documentation: API_DOCUMENTATION.md\n";
    echo "🧪 Testing: test_api.php\n\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

?>
