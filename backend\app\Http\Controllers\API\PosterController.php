<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Poster;
use Illuminate\Http\Request;

class PosterController extends Controller
{
    public function index(Request $request)
    {
        $category = $request->query('category');
        
        if ($category) {
            return Poster::where('category', $category)->get();
        }
        
        return Poster::all();
    }

    public function show($id)
    {
        return Poster::findOrFail($id);
    }

    // Admin methods (add authentication middleware in routes)
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:0',
            'image_path' => 'required|string',
            'category' => 'required|in:music,movie',
        ]);

        return Poster::create($validated);
    }

    public function update(Request $request, $id)
    {
        $poster = Poster::findOrFail($id);
        
        $validated = $request->validate([
            'title' => 'string|max:255',
            'description' => 'string',
            'price' => 'numeric|min:0',
            'quantity' => 'integer|min:0',
            'image_path' => 'string',
            'category' => 'in:music,movie',
        ]);

        $poster->update($validated);
        return $poster;
    }

    public function destroy($id)
    {
        $poster = Poster::findOrFail($id);
        $poster->delete();
        return response()->json(['message' => 'Poster deleted successfully']);
    }
}