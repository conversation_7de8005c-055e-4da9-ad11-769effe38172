<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Poster;
use Illuminate\Http\Request;

class PosterController extends Controller
{
    public function index(Request $request)
    {
        $query = Poster::available();

        // Filter by category
        if ($request->has('category')) {
            $query->byCategory($request->category);
        }

        // Filter by featured
        if ($request->has('featured')) {
            $query->featured();
        }

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('artist', 'like', "%{$search}%");
            });
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['title', 'price', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = $request->get('per_page', 12);

        return $query->paginate($perPage);
    }

    public function show($id)
    {
        return Poster::findOrFail($id);
    }

    // Admin methods (add authentication middleware in routes)
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:0',
            'image_path' => 'required|string',
            'category' => 'required|in:music,movie,art,sports,vintage',
            'artist' => 'nullable|string|max:255',
            'size' => 'required|in:A4,A3,A2,A1,50x70,70x100',
            'frame_type' => 'required|in:none,black,white,wood,gold',
            'tags' => 'nullable|array',
            'featured' => 'boolean',
            'status' => 'required|in:active,inactive,out_of_stock'
        ]);

        return Poster::create($validated);
    }

    public function update(Request $request, $id)
    {
        $poster = Poster::findOrFail($id);

        $validated = $request->validate([
            'title' => 'string|max:255',
            'description' => 'string',
            'price' => 'numeric|min:0',
            'quantity' => 'integer|min:0',
            'image_path' => 'string',
            'category' => 'in:music,movie,art,sports,vintage',
            'artist' => 'nullable|string|max:255',
            'size' => 'in:A4,A3,A2,A1,50x70,70x100',
            'frame_type' => 'in:none,black,white,wood,gold',
            'tags' => 'nullable|array',
            'featured' => 'boolean',
            'status' => 'in:active,inactive,out_of_stock'
        ]);

        $poster->update($validated);
        return $poster;
    }

    public function destroy($id)
    {
        $poster = Poster::findOrFail($id);
        $poster->delete();
        return response()->json(['message' => 'Poster deleted successfully']);
    }
}