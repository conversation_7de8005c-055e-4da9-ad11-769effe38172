<?php

/**
 * Complete I-POSTER E-commerce API
 * Handles all backend functionality for the poster shop
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Session-ID');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/backend/', '', $path);
$path = trim($path, '/');

// Get request body for POST/PUT requests
$input = json_decode(file_get_contents('php://input'), true);

// Session management
session_start();
$sessionId = $_SERVER['HTTP_X_SESSION_ID'] ?? session_id();

// Route handling
switch ($path) {
    case 'api/posters':
        handlePosters($pdo, $method, $input);
        break;

    case 'api/cart':
        handleCart($pdo, $method, $input, $sessionId);
        break;

    case 'api/cart/add':
        handleCartAdd($pdo, $method, $input, $sessionId);
        break;

    case 'api/orders':
        handleOrders($pdo, $method, $input, $sessionId);
        break;

    case 'api/auth/login':
        handleLogin($pdo, $method, $input);
        break;

    case 'api/auth/register':
        handleRegister($pdo, $method, $input);
        break;

    default:
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Endpoint not found'
        ]);
        break;
}

/**
 * Handle poster-related requests
 */
function handlePosters($pdo, $method, $input) {
    if ($method !== 'GET') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        $category = $_GET['category'] ?? null;
        $featured = $_GET['featured'] ?? null;
        $limit = $_GET['limit'] ?? null;

        $sql = "SELECT * FROM posters WHERE status = 'active'";
        $params = [];

        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }

        if ($featured !== null) {
            $sql .= " AND featured = ?";
            $params[] = (int)$featured;
        }

        $sql .= " ORDER BY created_at DESC";

        if ($limit) {
            $sql .= " LIMIT ?";
            $params[] = (int)$limit;
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $posters = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process poster data
        foreach ($posters as &$poster) {
            if (isset($poster['tags']) && $poster['tags']) {
                $poster['tags'] = json_decode($poster['tags'], true);
            }
            $poster['featured'] = (bool)$poster['featured'];
            $poster['price'] = (float)$poster['price'];
            $poster['quantity'] = (int)$poster['quantity'];
        }

        echo json_encode([
            'success' => true,
            'data' => $posters,
            'total' => count($posters)
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to fetch posters: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle cart requests
 */
function handleCart($pdo, $method, $input, $sessionId) {
    switch ($method) {
        case 'GET':
            getCart($pdo, $sessionId);
            break;
        case 'DELETE':
            clearCart($pdo, $sessionId);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
}

/**
 * Get cart contents
 */
function getCart($pdo, $sessionId) {
    try {
        $cartId = getOrCreateCart($pdo, $sessionId);

        $sql = "
            SELECT ci.*, p.title, p.description, p.price, p.image_path, p.category, p.artist
            FROM cart_items ci
            JOIN posters p ON ci.poster_id = p.id
            WHERE ci.cart_id = ?
            ORDER BY ci.created_at DESC
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$cartId]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $total = 0;
        $count = 0;

        foreach ($items as &$item) {
            $item['price'] = (float)$item['price'];
            $item['quantity'] = (int)$item['quantity'];
            $item['subtotal'] = $item['price'] * $item['quantity'];
            $total += $item['subtotal'];
            $count += $item['quantity'];
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'cart_id' => $cartId,
                'items' => $items,
                'total' => round($total, 2),
                'count' => $count,
                'currency' => 'MAD'
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to fetch cart: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle adding items to cart
 */
function handleCartAdd($pdo, $method, $input, $sessionId) {
    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        // Validate input
        if (!isset($input['poster_id']) || !is_numeric($input['poster_id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid poster ID']);
            return;
        }

        $posterId = (int)$input['poster_id'];
        $quantity = isset($input['quantity']) ? (int)$input['quantity'] : 1;

        if ($quantity < 1 || $quantity > 10) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Quantity must be between 1 and 10']);
            return;
        }

        // Check if poster exists and is available
        $stmt = $pdo->prepare("SELECT * FROM posters WHERE id = ? AND status = 'active'");
        $stmt->execute([$posterId]);
        $poster = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$poster) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Poster not found']);
            return;
        }

        if ($poster['quantity'] < $quantity) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Insufficient stock']);
            return;
        }

        $cartId = getOrCreateCart($pdo, $sessionId);

        // Check if item already exists in cart
        $stmt = $pdo->prepare("SELECT * FROM cart_items WHERE cart_id = ? AND poster_id = ?");
        $stmt->execute([$cartId, $posterId]);
        $existingItem = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingItem) {
            // Update quantity
            $newQuantity = $existingItem['quantity'] + $quantity;
            if ($newQuantity > 10) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Maximum 10 items per poster']);
                return;
            }

            $stmt = $pdo->prepare("UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$newQuantity, $existingItem['id']]);
        } else {
            // Add new item
            $stmt = $pdo->prepare("INSERT INTO cart_items (cart_id, poster_id, quantity, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
            $stmt->execute([$cartId, $posterId, $quantity]);
        }

        echo json_encode([
            'success' => true,
            'message' => 'Item added to cart successfully',
            'poster' => [
                'id' => $poster['id'],
                'title' => $poster['title'],
                'price' => (float)$poster['price']
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to add item to cart: ' . $e->getMessage()
        ]);
    }
}

/**
 * Clear cart
 */
function clearCart($pdo, $sessionId) {
    try {
        $cartId = getOrCreateCart($pdo, $sessionId);

        $stmt = $pdo->prepare("DELETE FROM cart_items WHERE cart_id = ?");
        $stmt->execute([$cartId]);

        echo json_encode([
            'success' => true,
            'message' => 'Cart cleared successfully'
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to clear cart: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle order requests
 */
function handleOrders($pdo, $method, $input, $sessionId) {
    switch ($method) {
        case 'POST':
            createOrder($pdo, $input, $sessionId);
            break;
        case 'GET':
            getOrders($pdo, $sessionId);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
}

/**
 * Create new order (Cash on Delivery)
 */
function createOrder($pdo, $input, $sessionId) {
    try {
        // Validate required fields
        $required = ['customer_name', 'customer_phone', 'delivery_address'];
        foreach ($required as $field) {
            if (!isset($input[$field]) || empty(trim($input[$field]))) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => "Field '$field' is required"]);
                return;
            }
        }

        $pdo->beginTransaction();

        // Get cart items
        $cartId = getOrCreateCart($pdo, $sessionId);
        $stmt = $pdo->prepare("
            SELECT ci.*, p.title, p.description, p.price
            FROM cart_items ci
            JOIN posters p ON ci.poster_id = p.id
            WHERE ci.cart_id = ?
        ");
        $stmt->execute([$cartId]);
        $cartItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($cartItems)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Cart is empty']);
            return;
        }

        // Calculate total
        $totalAmount = 0;
        foreach ($cartItems as $item) {
            $totalAmount += $item['price'] * $item['quantity'];
        }

        // Generate order number
        $orderNumber = 'IP-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));

        // Create order
        $stmt = $pdo->prepare("
            INSERT INTO orders (
                order_number, customer_name, customer_email, customer_phone,
                delivery_address, notes, total_amount, payment_type, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'Cash on Delivery', 'pending', NOW(), NOW())
        ");

        $stmt->execute([
            $orderNumber,
            trim($input['customer_name']),
            isset($input['customer_email']) ? trim($input['customer_email']) : null,
            trim($input['customer_phone']),
            trim($input['delivery_address']),
            isset($input['notes']) ? trim($input['notes']) : null,
            $totalAmount
        ]);

        $orderId = $pdo->lastInsertId();

        // Create order items
        foreach ($cartItems as $item) {
            $stmt = $pdo->prepare("
                INSERT INTO order_items (
                    order_id, poster_id, quantity, price, poster_title, poster_description, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");

            $stmt->execute([
                $orderId,
                $item['poster_id'],
                $item['quantity'],
                $item['price'],
                $item['title'],
                $item['description']
            ]);
        }

        // Clear cart
        $stmt = $pdo->prepare("DELETE FROM cart_items WHERE cart_id = ?");
        $stmt->execute([$cartId]);

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Order placed successfully',
            'data' => [
                'order_id' => $orderId,
                'order_number' => $orderNumber,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'payment_type' => 'Cash on Delivery'
            ]
        ]);

    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to create order: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get orders for session/user
 */
function getOrders($pdo, $sessionId) {
    try {
        // For now, we'll get orders by phone number or email if provided
        $phone = $_GET['phone'] ?? null;
        $email = $_GET['email'] ?? null;

        if (!$phone && !$email) {
            echo json_encode([
                'success' => true,
                'data' => [],
                'message' => 'Please provide phone number or email to view orders'
            ]);
            return;
        }

        $sql = "SELECT * FROM orders WHERE ";
        $params = [];

        if ($phone) {
            $sql .= "customer_phone = ?";
            $params[] = $phone;
        }

        if ($email) {
            if ($phone) $sql .= " OR ";
            $sql .= "customer_email = ?";
            $params[] = $email;
        }

        $sql .= " ORDER BY created_at DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get order items for each order
        foreach ($orders as &$order) {
            $stmt = $pdo->prepare("SELECT * FROM order_items WHERE order_id = ?");
            $stmt->execute([$order['id']]);
            $order['items'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $order['total_amount'] = (float)$order['total_amount'];
        }

        echo json_encode([
            'success' => true,
            'data' => $orders
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to fetch orders: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle user login
 */
function handleLogin($pdo, $method, $input) {
    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        if (!isset($input['email']) || !isset($input['password'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Email and password are required']);
            return;
        }

        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$input['email']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user || !password_verify($input['password'], $user['password'])) {
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Invalid credentials']);
            return;
        }

        // Create session token
        $token = bin2hex(random_bytes(32));
        $stmt = $pdo->prepare("UPDATE users SET remember_token = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$token, $user['id']]);

        echo json_encode([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ],
                'token' => $token
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Login failed: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle user registration
 */
function handleRegister($pdo, $method, $input) {
    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        return;
    }

    try {
        $required = ['name', 'email', 'password'];
        foreach ($required as $field) {
            if (!isset($input[$field]) || empty(trim($input[$field]))) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => "Field '$field' is required"]);
                return;
            }
        }

        // Validate email
        if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid email format']);
            return;
        }

        // Check if email already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$input['email']]);
        if ($stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Email already registered']);
            return;
        }

        // Create user
        $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (name, email, password, phone, address, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, NOW(), NOW())
        ");

        $stmt->execute([
            trim($input['name']),
            trim($input['email']),
            $hashedPassword,
            isset($input['phone']) ? trim($input['phone']) : null,
            isset($input['address']) ? trim($input['address']) : null
        ]);

        $userId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'Registration successful',
            'data' => [
                'user' => [
                    'id' => $userId,
                    'name' => $input['name'],
                    'email' => $input['email'],
                    'role' => 'customer'
                ]
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Registration failed: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get or create cart for session
 */
function getOrCreateCart($pdo, $sessionId) {
    // Check if cart exists for this session
    $stmt = $pdo->prepare("SELECT id FROM carts WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($cart) {
        return $cart['id'];
    }

    // Create new cart
    $stmt = $pdo->prepare("INSERT INTO carts (session_id, created_at, updated_at) VALUES (?, NOW(), NOW())");
    $stmt->execute([$sessionId]);

    return $pdo->lastInsertId();
}

?>
