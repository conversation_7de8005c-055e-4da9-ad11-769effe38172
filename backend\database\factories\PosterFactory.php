<?php

namespace Database\Factories;

use App\Models\Poster;
use Illuminate\Database\Eloquent\Factories\Factory;

class PosterFactory extends Factory
{
    protected $model = Poster::class;

    public function definition(): array
    {
        $categories = ['music', 'movie'];
        $category = $this->faker->randomElement($categories);
        
        // Different titles based on category
        if ($category === 'music') {
            $artists = ['<PERSON>', 'Taylor Swift', 'The Weeknd', 'Billie Eilish', '<PERSON><PERSON> Lamar', '<PERSON> the Creator', '<PERSON> Scott', 'Dua Lipa'];
            $albums = ['Certified Lover Boy', 'Midnights', 'After Hours', 'Happier Than Ever', 'Mr. Morale & The Big Steppers', 'Flower Boy', 'Utopia', 'Future Nostalgia'];
            $title = $this->faker->randomElement($albums);
            $description = $this->faker->randomElement($artists) . ' - ' . $title;
        } else {
            $movies = ['The Godfather', 'Pulp Fiction', 'Inception', 'The Dark Knight', 'Interstellar', 'Peaky Blinders', 'Men in Black', 'The Matrix'];
            $years = ['1972', '1994', '2010', '2008', '2014', '2013', '1997', '1999'];
            $index = $this->faker->numberBetween(0, 7);
            $title = $movies[$index];
            $description = $title . ' (' . $years[$index] . ')';
        }

        return [
            'title' => $title,
            'description' => $description,
            'price' => $this->faker->randomFloat(2, 50, 200),
            'quantity' => $this->faker->numberBetween(5, 50),
            'image_path' => '/storage/posters/' . ($category === 'music' ? 'music_' : 'movie_') . $this->faker->numberBetween(1, 5) . '.jpg',
            'category' => $category,
        ];
    }

    /**
     * Indicate that the poster is a music poster.
     */
    public function music(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => 'music',
            ];
        });
    }

    /**
     * Indicate that the poster is a movie poster.
     */
    public function movie(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => 'movie',
            ];
        });
    }
}