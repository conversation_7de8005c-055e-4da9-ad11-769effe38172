<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function items()
    {
        return $this->hasMany(CartItem::class);
    }

    // Helper methods
    public function getTotalPrice()
    {
        return $this->items->sum(function ($item) {
            return $item->quantity * $item->poster->price;
        });
    }

    public function getTotalItems()
    {
        return $this->items->sum('quantity');
    }

    public function addItem($posterId, $quantity = 1)
    {
        $existingItem = $this->items()->where('poster_id', $posterId)->first();

        if ($existingItem) {
            $existingItem->increment('quantity', $quantity);
            return $existingItem;
        }

        return $this->items()->create([
            'poster_id' => $posterId,
            'quantity' => $quantity
        ]);
    }

    public function removeItem($posterId)
    {
        return $this->items()->where('poster_id', $posterId)->delete();
    }

    public function updateItemQuantity($posterId, $quantity)
    {
        $item = $this->items()->where('poster_id', $posterId)->first();
        
        if ($item) {
            if ($quantity <= 0) {
                return $item->delete();
            }
            
            $item->update(['quantity' => $quantity]);
            return $item;
        }

        return null;
    }

    public function clear()
    {
        return $this->items()->delete();
    }
}
