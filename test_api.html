<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>I-POSTER API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .poster {
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: #2a2a2a;
        }
        .poster h3 {
            color: #e60026;
            margin: 0 0 10px 0;
        }
        .tags {
            display: flex;
            gap: 5px;
            margin: 10px 0;
        }
        .tag {
            background: #444;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .featured {
            background: #e60026;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        button {
            background: #e60026;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #c5001f;
        }
        #loading {
            text-align: center;
            color: #888;
        }
    </style>
</head>
<body>
    <h1>🎬 I-POSTER API Test</h1>
    <p>Testing the backend API with underrated posters</p>
    
    <div>
        <button onclick="loadAllPosters()">Load All Posters</button>
        <button onclick="loadMusicPosters()">Load Music Posters</button>
        <button onclick="loadMoviePosters()">Load Movie Posters</button>
        <button onclick="loadFeaturedPosters()">Load Featured Posters</button>
    </div>
    
    <div id="loading" style="display: none;">Loading...</div>
    <div id="results"></div>

    <script>
        const API_URL = 'http://localhost:8000';
        
        async function loadPosters(params = '') {
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            
            loading.style.display = 'block';
            results.innerHTML = '';
            
            try {
                const response = await fetch(`${API_URL}/posters${params}`);
                const data = await response.json();
                
                loading.style.display = 'none';
                
                if (data.error) {
                    results.innerHTML = `<p style="color: red;">Error: ${data.error}</p>`;
                    return;
                }
                
                const posters = data.data || data;
                
                if (posters.length === 0) {
                    results.innerHTML = '<p>No posters found.</p>';
                    return;
                }
                
                let html = `<h2>Found ${posters.length} posters</h2>`;
                
                posters.forEach(poster => {
                    html += `
                        <div class="poster">
                            <h3>${poster.title}</h3>
                            <p><strong>Description:</strong> ${poster.description}</p>
                            <p><strong>Price:</strong> $${poster.price}</p>
                            <p><strong>Category:</strong> ${poster.category}</p>
                            ${poster.artist ? `<p><strong>Artist:</strong> ${poster.artist}</p>` : ''}
                            <p><strong>Size:</strong> ${poster.size} | <strong>Frame:</strong> ${poster.frame_type}</p>
                            <p><strong>Stock:</strong> ${poster.quantity}</p>
                            ${poster.featured ? '<span class="featured">FEATURED</span>' : ''}
                            ${poster.tags ? `
                                <div class="tags">
                                    ${poster.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                                </div>
                            ` : ''}
                        </div>
                    `;
                });
                
                results.innerHTML = html;
                
            } catch (error) {
                loading.style.display = 'none';
                results.innerHTML = `<p style="color: red;">Network Error: ${error.message}</p>`;
            }
        }
        
        function loadAllPosters() {
            loadPosters();
        }
        
        function loadMusicPosters() {
            loadPosters('?category=music');
        }
        
        function loadMoviePosters() {
            loadPosters('?category=movie');
        }
        
        function loadFeaturedPosters() {
            loadPosters('?featured=1');
        }
        
        // Load all posters on page load
        window.onload = () => {
            loadAllPosters();
        };
    </script>
</body>
</html>
