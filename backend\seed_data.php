<?php

/**
 * Data Seeding Script for I-POSTER
 * This script populates the database with underrated posters and admin user
 */

// Database configuration
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'poster_shop';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database successfully!\n";
    
    // Clear existing data
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $pdo->exec("TRUNCATE TABLE order_items");
    $pdo->exec("TRUNCATE TABLE orders");
    $pdo->exec("TRUNCATE TABLE cart_items");
    $pdo->exec("TRUNCATE TABLE carts");
    $pdo->exec("TRUNCATE TABLE posters");
    $pdo->exec("TRUNCATE TABLE users");
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "✅ Cleared existing data!\n";
    
    // Create admin user
    $adminPassword = password_hash('password123', PASSWORD_DEFAULT);
    $pdo->exec("
        INSERT INTO users (name, email, password, phone, address, role, created_at, updated_at) 
        VALUES (
            'Admin User', 
            '<EMAIL>', 
            '$adminPassword', 
            '+212600000000', 
            'Admin Address, Casablanca, Morocco', 
            'admin', 
            NOW(), 
            NOW()
        )
    ");
    echo "✅ Admin user created! (email: <EMAIL>, password: password123)\n";
    
    // Create test customer
    $customerPassword = password_hash('password123', PASSWORD_DEFAULT);
    $pdo->exec("
        INSERT INTO users (name, email, password, phone, address, role, created_at, updated_at) 
        VALUES (
            'Test Customer', 
            '<EMAIL>', 
            '$customerPassword', 
            '+212611111111', 
            '123 Test Street, Rabat, Morocco', 
            'customer', 
            NOW(), 
            NOW()
        )
    ");
    echo "✅ Test customer created! (email: <EMAIL>, password: password123)\n";
    
    // UNDERRATED MUSIC POSTERS
    $musicPosters = [
        [
            'title' => 'Blonde',
            'description' => 'Frank Ocean - Blonde (2016)',
            'price' => 120.00,
            'quantity' => 15,
            'image_path' => '/storage/posters/frank_ocean_blonde.jpg',
            'category' => 'music',
            'artist' => 'Frank Ocean',
            'size' => 'A3',
            'frame_type' => 'white',
            'tags' => '["r&b", "alternative", "minimalist", "underrated"]',
            'featured' => 1
        ],
        [
            'title' => 'Madvillainy',
            'description' => 'Madvillain - Madvillainy (2004)',
            'price' => 95.00,
            'quantity' => 12,
            'image_path' => '/storage/posters/madvillainy.jpg',
            'category' => 'music',
            'artist' => 'Madvillain',
            'size' => 'A3',
            'frame_type' => 'black',
            'tags' => '["hip-hop", "underground", "doom", "classic"]',
            'featured' => 1
        ],
        [
            'title' => 'In the Aeroplane Over the Sea',
            'description' => 'Neutral Milk Hotel - In the Aeroplane Over the Sea (1998)',
            'price' => 85.00,
            'quantity' => 8,
            'image_path' => '/storage/posters/neutral_milk_hotel.jpg',
            'category' => 'music',
            'artist' => 'Neutral Milk Hotel',
            'size' => 'A4',
            'frame_type' => 'wood',
            'tags' => '["indie", "folk", "psychedelic", "cult-classic"]',
            'featured' => 0
        ],
        [
            'title' => 'Cosmogramma',
            'description' => 'Flying Lotus - Cosmogramma (2010)',
            'price' => 110.00,
            'quantity' => 10,
            'image_path' => '/storage/posters/flying_lotus.jpg',
            'category' => 'music',
            'artist' => 'Flying Lotus',
            'size' => 'A2',
            'frame_type' => 'black',
            'tags' => '["electronic", "experimental", "jazz", "abstract"]',
            'featured' => 0
        ],
        [
            'title' => 'Spiderland',
            'description' => 'Slint - Spiderland (1991)',
            'price' => 90.00,
            'quantity' => 6,
            'image_path' => '/storage/posters/slint_spiderland.jpg',
            'category' => 'music',
            'artist' => 'Slint',
            'size' => 'A3',
            'frame_type' => 'none',
            'tags' => '["post-rock", "math-rock", "influential", "underground"]',
            'featured' => 0
        ],
        [
            'title' => 'Since I Left You',
            'description' => 'The Avalanches - Since I Left You (2000)',
            'price' => 105.00,
            'quantity' => 9,
            'image_path' => '/storage/posters/avalanches.jpg',
            'category' => 'music',
            'artist' => 'The Avalanches',
            'size' => 'A3',
            'frame_type' => 'white',
            'tags' => '["electronic", "sample-based", "plunderphonics", "unique"]',
            'featured' => 1
        ]
    ];
    
    foreach ($musicPosters as $poster) {
        $stmt = $pdo->prepare("
            INSERT INTO posters (title, description, price, quantity, image_path, category, artist, size, frame_type, tags, featured, status, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
        ");
        $stmt->execute([
            $poster['title'],
            $poster['description'],
            $poster['price'],
            $poster['quantity'],
            $poster['image_path'],
            $poster['category'],
            $poster['artist'],
            $poster['size'],
            $poster['frame_type'],
            $poster['tags'],
            $poster['featured']
        ]);
    }
    echo "✅ Music posters added!\n";
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "🎵 Added " . count($musicPosters) . " underrated music posters\n";
    echo "👤 Admin login: <EMAIL> / password123\n";
    echo "👤 Customer login: <EMAIL> / password123\n";
    echo "\n🚀 Ready to start the API server!\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
