<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>I-POSTER API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #e60026;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #b8001f;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        h1 {
            color: #e60026;
            text-align: center;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #e60026;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>🎨 I-POSTER API Test Suite</h1>
    
    <div class="test-section">
        <h2>📦 Posters API</h2>
        <button class="test-button" onclick="testGetAllPosters()">Get All Posters</button>
        <button class="test-button" onclick="testGetMusicPosters()">Get Music Posters</button>
        <button class="test-button" onclick="testGetMoviePosters()">Get Movie Posters</button>
        <div id="posters-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🛒 Cart API</h2>
        <button class="test-button" onclick="testGetCart()">Get Cart</button>
        <button class="test-button" onclick="testAddToCart()">Add Item to Cart</button>
        <button class="test-button" onclick="testClearCart()">Clear Cart</button>
        <div id="cart-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>👤 Authentication API</h2>
        <button class="test-button" onclick="testRegister()">Test Register</button>
        <button class="test-button" onclick="testLogin()">Test Login</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📋 Orders API</h2>
        <button class="test-button" onclick="testCreateOrder()">Create Test Order</button>
        <button class="test-button" onclick="testTrackOrder()">Track Order</button>
        <div id="orders-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let sessionId = 'test_session_' + Date.now();
        let lastOrderNumber = null;

        // Helper function to make API calls
        async function apiCall(endpoint, options = {}) {
            const defaultHeaders = {
                'Content-Type': 'application/json',
                'X-Session-ID': sessionId
            };

            const config = {
                headers: { ...defaultHeaders, ...options.headers },
                ...options
            };

            try {
                const response = await fetch(`${API_BASE}${endpoint}`, config);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Display results
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.className = 'result ' + (result.success ? 'success' : 'error');
            element.textContent = JSON.stringify(result, null, 2);
        }

        // Poster API Tests
        async function testGetAllPosters() {
            const result = await apiCall('/api/posters');
            displayResult('posters-result', result);
        }

        async function testGetMusicPosters() {
            const result = await apiCall('/api/posters?category=music');
            displayResult('posters-result', result);
        }

        async function testGetMoviePosters() {
            const result = await apiCall('/api/posters?category=movie');
            displayResult('posters-result', result);
        }

        // Cart API Tests
        async function testGetCart() {
            const result = await apiCall('/api/cart');
            displayResult('cart-result', result);
        }

        async function testAddToCart() {
            const result = await apiCall('/api/cart/add', {
                method: 'POST',
                body: JSON.stringify({
                    poster_id: 1,
                    quantity: 2
                })
            });
            displayResult('cart-result', result);
        }

        async function testClearCart() {
            const result = await apiCall('/api/cart', {
                method: 'DELETE'
            });
            displayResult('cart-result', result);
        }

        // Auth API Tests
        async function testRegister() {
            const result = await apiCall('/api/auth/register', {
                method: 'POST',
                body: JSON.stringify({
                    name: 'Test User ' + Date.now(),
                    email: 'test' + Date.now() + '@example.com',
                    password: 'password123',
                    phone: '+212600000000'
                })
            });
            displayResult('auth-result', result);
        }

        async function testLogin() {
            const result = await apiCall('/api/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'password123'
                })
            });
            displayResult('auth-result', result);
        }

        // Orders API Tests
        async function testCreateOrder() {
            // First add an item to cart
            await testAddToCart();
            
            const result = await apiCall('/api/orders', {
                method: 'POST',
                body: JSON.stringify({
                    customer_name: 'Test Customer',
                    customer_email: '<EMAIL>',
                    customer_phone: '+212600123456',
                    delivery_address: '123 Test Street, Casablanca, Morocco',
                    notes: 'Test order from API test suite'
                })
            });
            
            if (result.success && result.data.data) {
                lastOrderNumber = result.data.data.order_number;
            }
            
            displayResult('orders-result', result);
        }

        async function testTrackOrder() {
            if (!lastOrderNumber) {
                displayResult('orders-result', {
                    success: false,
                    error: 'No order number available. Create an order first.'
                });
                return;
            }

            const result = await apiCall(`/orders/track?order_number=${lastOrderNumber}&phone=+212600123456`);
            displayResult('orders-result', result);
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            console.log('🧪 I-POSTER API Test Suite Loaded');
            console.log('Session ID:', sessionId);
            
            // Run a basic connectivity test
            testGetAllPosters();
        };
    </script>
</body>
</html>
