import React, { useState, useEffect } from "react";
import { postersAPI } from "../services/api";
import PosterCard from "./PosterCard";

function AlbumPosters() {
  const [posters, setPosters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchAlbumPosters();
  }, []);

  const fetchAlbumPosters = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the correct API endpoint
      const response = await postersAPI.getAll({ category: 'album' });
      console.log('Album API Response:', response.data); // Debug log

      if (response.data.success && response.data.data) {
        setPosters(response.data.data);
      } else {
        setPosters([]);
        setError('No album posters found');
      }
    } catch (error) {
      console.error('Error fetching album posters:', error);
      setError(`Failed to load album posters: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Error</h2>
          <p className="text-gray-400">{error}</p>
          <button
            onClick={fetchAlbumPosters}
            className="mt-4 bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            💿 ALBUM POSTERS
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Iconic album covers that defined generations. From classic rock masterpieces to 
            groundbreaking experimental albums, find the perfect poster to showcase your musical heritage.
          </p>
        </div>

        {/* Posters Grid */}
        {posters.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {posters.map((poster) => (
              <PosterCard key={poster.id} poster={poster} />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <h3 className="text-2xl font-semibold text-white mb-4">
              No Album Posters Found
            </h3>
            <p className="text-gray-400">
              Check back soon for new additions to our album collection!
            </p>
          </div>
        )}

        {/* Featured Albums Section */}
        <div className="mt-16 bg-gray-800 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-white mb-4">
            Legendary Album Covers
          </h2>
          <div className="grid md:grid-cols-2 gap-6 text-gray-300">
            <div>
              <h3 className="font-semibold text-white mb-2">Pink Floyd - The Dark Side of the Moon</h3>
              <p className="text-sm">
                One of the most recognizable album covers ever created, featuring the iconic prism design that became a symbol of progressive rock.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">The Beatles - Abbey Road</h3>
              <p className="text-sm">
                The famous crosswalk photo that became one of the most imitated and parodied album covers in music history.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">Nirvana - Nevermind</h3>
              <p className="text-sm">
                The underwater baby photo that perfectly captured the raw energy and spirit of the grunge movement.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">The Velvet Underground & Nico</h3>
              <p className="text-sm">
                Andy Warhol's iconic banana design that you could actually peel, merging pop art with underground music.
              </p>
            </div>
          </div>
        </div>

        {/* Album vs Music Section */}
        <div className="mt-16 bg-gradient-to-r from-red-900 to-red-800 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-white mb-4">
            Albums vs Music Posters - What's the Difference?
          </h2>
          <div className="grid md:grid-cols-2 gap-8 text-white">
            <div>
              <h3 className="font-semibold text-red-200 mb-3">💿 Album Posters</h3>
              <ul className="space-y-2 text-sm">
                <li>• Classic album covers from legendary artists</li>
                <li>• Iconic designs that defined music history</li>
                <li>• Perfect for music lovers and collectors</li>
                <li>• Timeless artwork that never goes out of style</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-red-200 mb-3">🎵 Music Posters</h3>
              <ul className="space-y-2 text-sm">
                <li>• Underrated and experimental music discoveries</li>
                <li>• Hidden gems and cult classics</li>
                <li>• For those who love exploring new sounds</li>
                <li>• Unique finds that showcase your musical taste</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AlbumPosters;
