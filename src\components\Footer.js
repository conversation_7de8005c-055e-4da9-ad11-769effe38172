import React from "react";
import { Link } from "react-router-dom";

function Footer() {
  return (
    <footer className="bg-primary-dark border-t border-secondary-dark">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* About Section */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold text-pure-white mb-4">I-POSTER</h3>
            <p className="text-neutral-medium mb-6 max-w-md">
              Discover a curated collection of underrated posters from music, movies, and art.
              Bring unique character to your walls with our exclusive designs celebrating hidden gems.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://instagram.com"
                target="_blank"
                rel="noreferrer"
                className="text-neutral-medium hover:text-accent-red-light transition-colors duration-200 p-2 rounded-lg hover:bg-secondary-dark"
              >
                <i className="fa-brands fa-instagram text-xl"></i>
              </a>
              <a
                href="https://facebook.com"
                target="_blank"
                rel="noreferrer"
                className="text-neutral-medium hover:text-accent-red-light transition-colors duration-200 p-2 rounded-lg hover:bg-secondary-dark"
              >
                <i className="fa-brands fa-facebook text-xl"></i>
              </a>
              <a
                href="https://tiktok.com"
                target="_blank"
                rel="noreferrer"
                className="text-neutral-medium hover:text-accent-red-light transition-colors duration-200 p-2 rounded-lg hover:bg-secondary-dark"
              >
                <i className="fa-brands fa-tiktok text-xl"></i>
              </a>
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noreferrer"
                className="text-neutral-medium hover:text-accent-red-light transition-colors duration-200 p-2 rounded-lg hover:bg-secondary-dark"
              >
                <i className="fa-brands fa-twitter text-xl"></i>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/music-posters" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Music Posters
                </Link>
              </li>
              <li>
                <Link to="/movies-posters" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Movie Posters
                </Link>
              </li>
              <li>
                <Link to="/cart" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Shopping Cart
                </Link>
              </li>
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Customer Service</h3>
            <ul className="space-y-2">
              <li>
                <a href="#contact" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Contact Us
                </a>
              </li>
              <li>
                <a href="#shipping" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Shipping Info
                </a>
              </li>
              <li>
                <a href="#returns" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Returns & Exchanges
                </a>
              </li>
              <li>
                <a href="#faq" className="text-gray-400 hover:text-white transition-colors duration-200">
                  FAQ
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Payment & Delivery Info */}
        <div className="mt-8 pt-8 border-t border-gray-800">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-white font-semibold mb-2">Payment Method</h4>
              <p className="text-gray-400 text-sm">
                💰 Cash on Delivery (COD) - Pay when you receive your order
              </p>
            </div>
            <div>
              <h4 className="text-white font-semibold mb-2">Delivery</h4>
              <p className="text-gray-400 text-sm">
                🚚 Free delivery across Morocco - 2-5 business days
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            &copy; 2024 I-POSTER. All rights reserved. Celebrating underrated art.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#terms" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
              Terms of Service
            </a>
            <a href="#privacy" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
              Privacy Policy
            </a>
            <a href="#cookies" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
              Cookie Policy
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
